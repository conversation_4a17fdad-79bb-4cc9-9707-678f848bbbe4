using Plots
using Statistics

"""
    simulate_toom_1d_lrc(L, T; p=0.1, q=0.1, antiperiodic=false)

Simulate the one-dimensional analog of <PERSON><PERSON>'s LRC (Left-Right-Center) cellular automaton with noise.
This is the 1D version of the NEC model where:
- L (Left) corresponds to N (North) in 2D
- R (Right) corresponds to E (East) in 2D  
- C (Center) remains the same

Parameters:
- L: length of the 1D lattice
- T: number of time steps
- p: probability of error when majority is 1
- q: probability of error when majority is 0
- antiperiodic: if true, use antiperiodic boundary conditions; if false, use periodic

Returns the full state history for visualization.
"""
function simulate_toom_1d_lrc(L, T; p=0.1, q=0.1, antiperiodic=false)
    # Initialize state randomly (0 or 1) or start with zeros
    state = zeros(Int, L)
    
    # Optional: Set middle region to 1s for initial condition
    # middle_start = div(L, 4) + 1
    # middle_end = middle_start + div(L, 2) - 1
    # for i in middle_start:middle_end
    #     state[i] = 1
    # end
    
    # Store history for visualization
    history = zeros(Int, T+1, L)
    densities = zeros(Float64, T+1)
    history[1, :] = state
    densities[1] = mean(state)
    
    for t in 1:T
        new_state = similar(state)
        
        for i in 1:L
            # Get left neighbor with selected boundary condition
            if i == 1 && antiperiodic
                left = 1 - state[L]  # Antiperiodic boundary
            else
                left = state[mod1(i-1, L)]  # Periodic boundary
            end
            
            # Get right neighbor with selected boundary condition  
            if i == L && antiperiodic
                right = 1 - state[1]  # Antiperiodic boundary
            else
                right = state[mod1(i+1, L)]  # Periodic boundary
            end
            
            center = state[i]
            
            # Apply majority rule (same as 2D version)
            maj = majority(left, right, center)
            
            # Apply noise
            if maj == 1
                new_state[i] = rand() < p ? 0 : 1
            else
                new_state[i] = rand() < q ? 1 : 0
            end
        end
        
        state = new_state
        @show t, mean(state)
        
        history[t+1, :] = state
        densities[t+1] = mean(state)
    end
    
    return history, densities
end

# Reuse the majority function from the 2D version
majority(l, r, c) = (l & r) | (l & c) | (r & c)

"""
    visualize_toom_1d(history, p, q, antiperiodic)
    
Create a visualization of the 1D Toom LRC simulation as a space-time diagram.
"""
function visualize_toom_1d(history, p, q, antiperiodic)
    T, L = size(history)
    
    # Create folder based on boundary conditions
    folder_name = antiperiodic ? "DATA_1D_anti" : "DATA_1D_per"
    
    # Create directory if it doesn't exist
    mkpath(folder_name)
    
    # Create filename with boundary condition info
    filename = "$(folder_name)/spacetime_L$(L)_p$(p)q$(q).png"
    
    # Create space-time diagram
    heatmap(1:L, 1:T, history, 
            c=:grays,
            aspect_ratio=:auto,
            title="1D Toom's LRC Model - Space-Time Diagram",
            xlabel="Position", ylabel="Time",
            legend=false)
    
    savefig(filename)
    return filename
end

"""
    visualize_toom_1d_animation(history, p, q, antiperiodic)
    
Create an animated visualization of the 1D Toom LRC simulation.
"""
function visualize_toom_1d_animation(history, p, q, antiperiodic)
    T, L = size(history)
    
    # Create folder based on boundary conditions
    folder_name = antiperiodic ? "DATA_1D_anti" : "DATA_1D_per"
    
    # Create directory if it doesn't exist
    mkpath(folder_name)
    
    # Create filename with boundary condition info
    filename = "$(folder_name)/animation_L$(L)_p$(p)q$(q).gif"
    
    anim = @animate for t in 1:T
        plot(1:L, history[t, :], 
             ylim=(-0.1, 1.1),
             marker=:circle,
             markersize=4,
             linewidth=2,
             title="1D Toom's LRC Model (t=$t)",
             xlabel="Position", ylabel="State",
             legend=false)
    end
    
    return gif(anim, filename, fps=5)
end

"""
    analyze_density_1d(densities, L, p, q, antiperiodic)
    
Analyze and plot the density evolution for the 1D Toom model.
"""
function analyze_density_1d(densities, L, p, q, antiperiodic)
    T = length(densities)
    
    # Create folder based on boundary conditions
    folder_name = antiperiodic ? "DATA_1D_anti" : "DATA_1D_per"
    
    # Create directory if it doesn't exist
    mkpath(folder_name)
    
    # Create filename with boundary condition info
    filename = "$(folder_name)/density_L$(L)_p$(p)q$(q).png"
    
    plot(1:T, densities, 
         xlabel="Time", ylabel="Density of 1s",
         title="Density Evolution in 1D Toom's LRC Model",
         linewidth=2,
         legend=false)
    
    savefig(filename)
    return densities
end

# Example simulation run
L = 50  # Length of 1D lattice
T = 20*L  # Number of time steps
p = 0.02  # Error probability when majority is 1
q = 0.02  # Error probability when majority is 0
antiperiodic = false  # Boundary condition

@show L, T, p, q, antiperiodic

# Run simulation
@time history, densities = simulate_toom_1d_lrc(L, T, p=p, q=q, antiperiodic=antiperiodic)

# Create visualizations
@time spacetime_file = visualize_toom_1d(history, p, q, antiperiodic)
@time animation_file = visualize_toom_1d_animation(history, p, q, antiperiodic)
@time density_result = analyze_density_1d(densities, L, p, q, antiperiodic)

println("Simulation complete!")
println("Space-time diagram saved as: $spacetime_file")
println("Animation saved as: $animation_file")
