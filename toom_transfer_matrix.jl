using LinearAlgebra
using SparseArrays
using Printf
using Statistics

⊗(A,B) = kron(A,B)
xp = [0.5, 0.5];

"""
    create_toom_transfer_matrix(Lx, Ly, p, q, x_antiperiodic=false, y_antiperiodic=false)

Create the transfer matrix for Too<PERSON>'s NEC model with given parameters.
The transfer matrix has dimensions 2^(Lx*Ly) × 2^(Lx*Ly).

Parameters:
- Lx, Ly: Grid dimensions
- p: Probability of error when majority is 1
- q: Probability of error when majority is 0
- x_antiperiodic, y_antiperiodic: Boundary conditions

Returns a sparse matrix representing the transfer operator.
"""
function create_toom_transfer_matrix(Lx, Ly, p, q; x_antiperiodic=false, y_antiperiodic=false)
    # Total number of states
    n_states = 2^(Lx * Ly)
    
    # Use sparse matrix for efficiency
    T = spzeros(Float64, n_states, n_states)
    
    # Helper function to convert state array to index
    function state_to_index(state)
        idx = 1
        for i in 1:Lx, j in 1:Ly
            idx += state[i, j] * 2^((i-1)*Ly + (j-1))
        end
        return idx
    end
    
    # Helper function to convert index to state array
    function index_to_state(idx)
        idx = idx - 1  # Adjust for 1-based indexing
        state = zeros(Int, Lx, Ly)
        for i in 1:Lx, j in 1:Ly
            power = (i-1)*Ly + (j-1)
            state[i, j] = (idx >> power) & 1
        end
        return state
    end
    
    # Majority function from toom.jl
    majority(n, e, c) = (n & e) | (n & c) | (e & c)
    
    # Build the transfer matrix
    println("Building transfer matrix (this may take a while)...")
    
    # For small systems, we can enumerate all transitions
    if Lx * Ly <= 12  # Only practical for small systems
        for idx_from in 1:n_states
            state_from = index_to_state(idx_from)
            
            # Calculate all possible next states and their probabilities
            next_states = Dict{Array{Int,2}, Float64}()
            
            # For each possible next state configuration
            for config in 0:(2^(Lx*Ly)-1)
                state_to = zeros(Int, Lx, Ly)
                prob = 1.0
                
                # Convert configuration to state
                for i in 1:Lx, j in 1:Ly
                    power = (i-1)*Ly + (j-1)
                    state_to[i, j] = (config >> power) & 1
                end
                
                # Check if this is a valid transition
                valid = true
                for x in 1:Lx, y in 1:Ly
                    # Get north neighbor with selected boundary condition
                    if y == Ly && y_antiperiodic
                        north = 1 - state_from[x, 1]  # Antiperiodic in y
                    else
                        north = state_from[x, mod1(y+1, Ly)]  # Periodic in y
                    end
                    
                    # Get east neighbor with selected boundary condition
                    if x == Lx && x_antiperiodic
                        east = 1 - state_from[1, y]  # Antiperiodic in x
                    else
                        east = state_from[mod1(x+1, Lx), y]  # Periodic in x
                    end
                    
                    center = state_from[x, y]
                    
                    # Apply majority rule
                    maj = majority(north, east, center)
                    
                    # Calculate transition probability
                    if maj == 1
                        if state_to[x, y] == 1
                            prob *= (1 - p)  # Probability of staying 1
                        else
                            prob *= p  # Probability of flipping to 0
                        end
                    else
                        if state_to[x, y] == 0
                            prob *= (1 - q)  # Probability of staying 0
                        else
                            prob *= q  # Probability of flipping to 1
                        end
                    end
                end
                
                # Add to transfer matrix
                idx_to = state_to_index(state_to)
                T[idx_to, idx_from] += prob
            end
        end
    else
        # For larger systems, use Monte Carlo sampling
        println("System too large for exact matrix. Using Monte Carlo sampling...")
        
        # Number of samples per state
        n_samples = 1000
        
        for idx_from in 1:min(n_states, 10000)  # Limit to first 10000 states
            state_from = index_to_state(idx_from)
            
            # Sample next states
            for _ in 1:n_samples
                new_state = similar(state_from)
                
                for x in 1:Lx, y in 1:Ly
                    # Get north neighbor with selected boundary condition
                    if y == Ly && y_antiperiodic
                        north = 1 - state_from[x, 1]  # Antiperiodic in y
                    else
                        north = state_from[x, mod1(y+1, Ly)]  # Periodic in y
                    end
                    
                    # Get east neighbor with selected boundary condition
                    if x == Lx && x_antiperiodic
                        east = 1 - state_from[1, y]  # Antiperiodic in x
                    else
                        east = state_from[mod1(x+1, Lx), y]  # Periodic in x
                    end
                    
                    center = state_from[x, y]
                    
                    # Apply majority rule
                    maj = majority(north, east, center)
                    
                    # Apply noise
                    if maj == 1
                        new_state[x, y] = rand() < p ? 0 : 1
                    else
                        new_state[x, y] = rand() < q ? 1 : 0
                    end
                end
                
                # Add to transfer matrix
                idx_to = state_to_index(new_state)
                T[idx_to, idx_from] += 1.0 / n_samples
            end
        end
    end
    
    return T
end

"""
    analyze_transfer_matrix(T, Lx, Ly)

Analyze the transfer matrix to find steady states and relaxation times.
Enhanced version with configuration analysis.
"""
function analyze_transfer_matrix(T, Lx, Ly)
    println("Analyzing transfer matrix...")

    # Helper function to convert index to state array
    function index_to_state(idx)
        idx = idx - 1  # Adjust for 1-based indexing
        state = zeros(Int, Lx, Ly)
        for i in 1:Lx, j in 1:Ly
            power = (i-1)*Ly + (j-1)
            state[i, j] = (idx >> power) & 1
        end
        return state
    end

    # Helper function to display a 2D configuration nicely
    function display_config(state)
        config_str = ""
        for i in 1:Lx
            for j in 1:Ly
                # @show size(state), size(state')
                config_str *= state[i, j] == 1 ? "1" : "0"
                # config_str *= state'[i, Ly+1-j] == 1 ? "1" : "0"
            end
            if i < Lx
                config_str *= "\n"
            end
        end
        return config_str
    end

    # Calculate eigenvalues and eigenvectors
    if size(T, 1) <= 2^16  # Increased limit for better analysis
        # Right eigendecomposition
        F_right = eigen(Array(T))
        eigenvalues_right = F_right.values
        eigenvectors_right = F_right.vectors

        # Left eigendecomposition (transpose the matrix)
        F_left = eigen(Array(T'))
        eigenvalues_left = F_left.values
        eigenvectors_left = F_left.vectors

        # Sort by magnitude of eigenvalues
        idx_right = sortperm(abs.(eigenvalues_right), rev=true)
        eigenvalues_right = eigenvalues_right[idx_right]
        eigenvectors_right = eigenvectors_right[:, idx_right]

        idx_left = sortperm(abs.(eigenvalues_left), rev=true)
        eigenvalues_left = eigenvalues_left[idx_left]
        eigenvectors_left = eigenvectors_left[:, idx_left]

        println("\n=== Eigenvalue Analysis ===")
        println("Largest eigenvalues (by magnitude):")
        for i in 1:min(8, length(eigenvalues_right))
            λ = eigenvalues_right[i]
            println(@sprintf("λ%d = %8.6f + %8.6fi (|λ| = %8.6f)",
                    i, real(λ), imag(λ), abs(λ)))
        end

        # Find steady states (eigenvalue ≈ 1)
        steady_indices_right = findall(x -> abs(abs(x) - 1.0) < 1e-10, eigenvalues_right)
        steady_indices_left = findall(x -> abs(abs(x) - 1.0) < 1e-10, eigenvalues_left)

        if !isempty(steady_indices_right)
            println("\n=== Steady State Analysis ===")
            println("Found $(length(steady_indices_right)) right steady state(s)")

            # Analyze the dominant steady state
            steady_idx = steady_indices_right[1]
            steady_state_right = eigenvectors_right[:, steady_idx]
            steady_state_right = abs.(steady_state_right) ./ sum(abs.(steady_state_right))  # Normalize

            println("Dominant right eigenvectors:")
            display(eigenvectors_right[:,1:2])

            # Show most probable configurations
            println("\nMost probable configurations in steady state:")
            sorted_indices = sortperm(steady_state_right, rev=true)

            for i in 1:min(10, length(sorted_indices))
                idx = sorted_indices[i]
                config = index_to_state(idx)
                prob = steady_state_right[idx]

                println(@sprintf("\nConfiguration %d (probability: %.6f):", i, prob))
                println(display_config(config))
            end

            # Compute average density
            avg_density = 0.0
            for idx in 1:length(steady_state_right)
                config = index_to_state(idx)
                density = mean(config)
                avg_density += steady_state_right[idx] * density
            end
            println(@sprintf("\nAverage density in steady state: %.6f", avg_density))

            # Relaxation time is related to the second largest eigenvalue
            if length(eigenvalues_right) >= 2
                λ2 = eigenvalues_right[2]
                if abs(λ2) > 1e-10
                    relaxation_time = -1.0 / log(abs(λ2))
                    println(@sprintf("Relaxation time: %.4f", relaxation_time))
                end
            end
        else
            println("\nNo right steady state found with eigenvalue 1.")
        end

        # Analyze left steady state
        if !isempty(steady_indices_left)
            steady_idx_left = steady_indices_left[1]
            steady_state_left = eigenvectors_left[:, steady_idx_left]
            steady_state_left = abs.(steady_state_left) ./ sum(abs.(steady_state_left))  # Normalize

            println("\nLeft steady state distribution found.")

            # Check biorthogonality if both steady states exist
            if !isempty(steady_indices_right)
                steady_state_right_normalized = abs.(eigenvectors_right[:, steady_indices_right[1]])
                steady_state_right_normalized ./= sum(steady_state_right_normalized)
                dot_product = abs(dot(steady_state_left, steady_state_right_normalized))
                println(@sprintf("Dot product of left and right steady states: %.6f", dot_product))
            end
        else
            println("\nNo left steady state found with eigenvalue 1.")
        end

        # Return comprehensive results
        return Dict(
            "right_eigenvalues" => eigenvalues_right,
            "right_eigenvectors" => eigenvectors_right,
            "left_eigenvalues" => eigenvalues_left,
            "left_eigenvectors" => eigenvectors_left,
            "steady_indices_right" => steady_indices_right,
            "steady_indices_left" => steady_indices_left
        )
    else
        println("Matrix too large for eigendecomposition.")
        return T
    end
end

# Example usage
Lx, Ly = 2, 4  # Small grid for demonstration
p, q = 0.02, 0.01
x_antiperiodic, y_antiperiodic = false, true

println("Creating transfer matrix for $(Lx)×$(Ly) grid...")
T = create_toom_transfer_matrix(Lx, Ly, p, q, 
                              x_antiperiodic=x_antiperiodic, 
                              y_antiperiodic=y_antiperiodic)

println("Transfer matrix size: $(size(T))")
println("Number of non-zero elements: $(nnz(T))")

# Analyze the transfer matrix
results = analyze_transfer_matrix(T, Lx, Ly)

println("\nAnalysis complete!")
# println(q/Ly/(p+q))