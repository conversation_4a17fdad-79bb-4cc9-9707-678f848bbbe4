using LinearAlgebra
using SparseArrays

⊗(A,B) = kron(A,B)
xp = [0.5, 0.5];

"""
    create_toom_transfer_matrix(Lx, Ly, p, q, x_antiperiodic=false, y_antiperiodic=false)

Create the transfer matrix for Too<PERSON>'s NEC model with given parameters.
The transfer matrix has dimensions 2^(Lx*Ly) × 2^(Lx*Ly).

Parameters:
- Lx, Ly: Grid dimensions
- p: Probability of error when majority is 1
- q: Probability of error when majority is 0
- x_antiperiodic, y_antiperiodic: Boundary conditions

Returns a sparse matrix representing the transfer operator.
"""
function create_toom_transfer_matrix(Lx, Ly, p, q; x_antiperiodic=false, y_antiperiodic=false)
    # Total number of states
    n_states = 2^(Lx * Ly)
    
    # Use sparse matrix for efficiency
    T = spzeros(Float64, n_states, n_states)
    
    # Helper function to convert state array to index
    function state_to_index(state)
        idx = 1
        for i in 1:Lx, j in 1:Ly
            idx += state[i, j] * 2^((i-1)*Ly + (j-1))
        end
        return idx
    end
    
    # Helper function to convert index to state array
    function index_to_state(idx)
        idx = idx - 1  # Adjust for 1-based indexing
        state = zeros(Int, Lx, Ly)
        for i in 1:Lx, j in 1:Ly
            power = (i-1)*Ly + (j-1)
            state[i, j] = (idx >> power) & 1
        end
        return state
    end
    
    # Majority function from toom.jl
    majority(n, e, c) = (n & e) | (n & c) | (e & c)
    
    # Build the transfer matrix
    println("Building transfer matrix (this may take a while)...")
    
    # For small systems, we can enumerate all transitions
    if Lx * Ly <= 10  # Only practical for small systems
        for idx_from in 1:n_states
            state_from = index_to_state(idx_from)
            
            # Calculate all possible next states and their probabilities
            next_states = Dict{Array{Int,2}, Float64}()
            
            # For each possible next state configuration
            for config in 0:(2^(Lx*Ly)-1)
                state_to = zeros(Int, Lx, Ly)
                prob = 1.0
                
                # Convert configuration to state
                for i in 1:Lx, j in 1:Ly
                    power = (i-1)*Ly + (j-1)
                    state_to[i, j] = (config >> power) & 1
                end
                
                # Check if this is a valid transition
                valid = true
                for x in 1:Lx, y in 1:Ly
                    # Get north neighbor with selected boundary condition
                    if y == Ly && y_antiperiodic
                        north = 1 - state_from[x, 1]  # Antiperiodic in y
                    else
                        north = state_from[x, mod1(y+1, Ly)]  # Periodic in y
                    end
                    
                    # Get east neighbor with selected boundary condition
                    if x == Lx && x_antiperiodic
                        east = 1 - state_from[1, y]  # Antiperiodic in x
                    else
                        east = state_from[mod1(x+1, Lx), y]  # Periodic in x
                    end
                    
                    center = state_from[x, y]
                    
                    # Apply majority rule
                    maj = majority(north, east, center)
                    
                    # Calculate transition probability
                    if maj == 1
                        if state_to[x, y] == 1
                            prob *= (1 - p)  # Probability of staying 1
                        else
                            prob *= p  # Probability of flipping to 0
                        end
                    else
                        if state_to[x, y] == 0
                            prob *= (1 - q)  # Probability of staying 0
                        else
                            prob *= q  # Probability of flipping to 1
                        end
                    end
                end
                
                # Add to transfer matrix
                idx_to = state_to_index(state_to)
                T[idx_to, idx_from] += prob
            end
        end
    else
        # For larger systems, use Monte Carlo sampling
        println("System too large for exact matrix. Using Monte Carlo sampling...")
        
        # Number of samples per state
        n_samples = 1000
        
        for idx_from in 1:min(n_states, 10000)  # Limit to first 10000 states
            state_from = index_to_state(idx_from)
            
            # Sample next states
            for _ in 1:n_samples
                new_state = similar(state_from)
                
                for x in 1:Lx, y in 1:Ly
                    # Get north neighbor with selected boundary condition
                    if y == Ly && y_antiperiodic
                        north = 1 - state_from[x, 1]  # Antiperiodic in y
                    else
                        north = state_from[x, mod1(y+1, Ly)]  # Periodic in y
                    end
                    
                    # Get east neighbor with selected boundary condition
                    if x == Lx && x_antiperiodic
                        east = 1 - state_from[1, y]  # Antiperiodic in x
                    else
                        east = state_from[mod1(x+1, Lx), y]  # Periodic in x
                    end
                    
                    center = state_from[x, y]
                    
                    # Apply majority rule
                    maj = majority(north, east, center)
                    
                    # Apply noise
                    if maj == 1
                        new_state[x, y] = rand() < p ? 0 : 1
                    else
                        new_state[x, y] = rand() < q ? 1 : 0
                    end
                end
                
                # Add to transfer matrix
                idx_to = state_to_index(new_state)
                T[idx_to, idx_from] += 1.0 / n_samples
            end
        end
    end
    
    return T
end

"""
    analyze_transfer_matrix(T)

Analyze the transfer matrix to find steady states and relaxation times.
"""
function analyze_transfer_matrix(T)
    println("Analyzing transfer matrix...")
    
    # Calculate eigenvalues and eigenvectors
    if size(T, 1) <= 100  # Only for small matrices
        # Right eigendecomposition
        F_right = eigen(Array(T))
        eigenvalues_right = F_right.values
        eigenvectors_right = F_right.vectors
        
        # Left eigendecomposition (transpose the matrix)
        F_left = eigen(Array(T'))
        eigenvalues_left = F_left.values
        eigenvectors_left = F_left.vectors
        
        # Sort by magnitude of eigenvalues
        idx_right = sortperm(abs.(eigenvalues_right), rev=true)
        eigenvalues_right = eigenvalues_right[idx_right]
        eigenvectors_right = eigenvectors_right[:, idx_right]
        
        idx_left = sortperm(abs.(eigenvalues_left), rev=true)
        eigenvalues_left = eigenvalues_left[idx_left]
        eigenvectors_left = eigenvectors_left[:, idx_left]
        
        println("\n=== Right Eigenvalues (T·v = λv) ===")
        println("Largest eigenvalues:")
        for i in 1:min(5, length(eigenvalues_right))
            println("λ$i = $(eigenvalues_right[i])")
        end
        
        println("\n=== Left Eigenvalues (u·T = λu) ===")
        println("Largest eigenvalues:")
        for i in 1:min(5, length(eigenvalues_left))
            println("λ$i = $(eigenvalues_left[i])")
        end
        
        # The steady state corresponds to the right eigenvector with eigenvalue 1
        steady_idx_right = findfirst(isapprox.(eigenvalues_right, 1.0, atol=1e-10))
        if !isnothing(steady_idx_right)
            steady_state_right = eigenvectors_right[:, steady_idx_right]
            steady_state_right = steady_state_right ./ sum(steady_state_right)  # Normalize
            
            println("\nRight steady state distribution found.")
            
            # Relaxation time is related to the second largest eigenvalue
            if length(eigenvalues_right) >= 2
                relaxation_time = -1.0 / log(abs(eigenvalues_right[2]))
                println("Relaxation time: $relaxation_time")
            end
        else
            println("\nNo right steady state found with eigenvalue 1.")
        end
        
        # The left steady state
        steady_idx_left = findfirst(isapprox.(eigenvalues_left, 1.0, atol=1e-10))
        if !isnothing(steady_idx_left)
            steady_state_left = eigenvectors_left[:, steady_idx_left]
            steady_state_left = steady_state_left ./ sum(steady_state_left)  # Normalize
            
            println("\nLeft steady state distribution found.")
            
            # Check biorthogonality
            if !isnothing(steady_idx_right)
                dot_product = abs(dot(steady_state_left, steady_state_right))
                println("Dot product of left and right steady states: $dot_product")
            end
        else
            println("\nNo left steady state found with eigenvalue 1.")
        end
        
        # Return both sets of eigenvalues and eigenvectors
        return Dict(
            "right_eigenvalues" => eigenvalues_right,
            "right_eigenvectors" => eigenvectors_right,
            "left_eigenvalues" => eigenvalues_left,
            "left_eigenvectors" => eigenvectors_left
        )
    else
        println("Matrix too large for eigendecomposition.")
        return T
    end
end

# Example usage
Lx, Ly = 2, 2  # Small grid for demonstration
p, q = 0.1, 0.1
x_antiperiodic, y_antiperiodic = true, false

println("Creating transfer matrix for $(Lx)×$(Ly) grid...")
T = create_toom_transfer_matrix(Lx, Ly, p, q, 
                              x_antiperiodic=x_antiperiodic, 
                              y_antiperiodic=y_antiperiodic)

println("Transfer matrix size: $(size(T))")
println("Number of non-zero elements: $(nnz(T))")

F = eigen(Array(T'), sortby =  λ -> -abs(λ))
@show F.values[1:2]
display(F.vectors[:,1:2])

F = eigen(Array(T), sortby =  λ -> -abs(λ))
@show F.values[1:5]
display(F.vectors[:,1:2])

# analyze_transfer_matrix(T)