using Plots
using Statistics

"""
    simulate_pca_1d_conditional(L, T; p=0.1, q=0.1, antiperiodic=false)

Simulate a one-dimensional PCA with conditional probabilities:
- P(0_j | 1_j, 1_{j+1}) = 1
- P(0_j | 0_j, 1_{j+1}) = 1-q  
- P(0_j | 1_j, 0_{j+1}) = p
- P(0_j | 0_j, 0_{j+1}) = 0

Parameters:
- L: length of the 1D lattice
- T: number of time steps
- p: probability P(0_j | 1_j, 0_{j+1})
- q: probability P(1_j | 0_j, 1_{j+1}) = 1 - P(0_j | 0_j, 1_{j+1})
- antiperiodic: if true, use antiperiodic boundary conditions; if false, use periodic

Returns the full state history for visualization.
"""
function simulate_pca_1d_conditional(L, T; p=0.1, q=0.1, antiperiodic=false)
    # Initialize state randomly (0 or 1) or start with zeros
    state = zeros(Int, L)
    # state = rand(0:1, L)
    state[L-2:L] .= 1

    # Optional: Set middle region to 1s for initial condition
    # middle_start = div(L, 4) + 1
    # middle_end = middle_start + div(L, 2) - 1
    # for i in middle_start:middle_end
    #     state[i] = 1
    # end
    
    # Store history for visualization
    history = zeros(Int, T+1, L)
    densities = zeros(Float64, T+1)
    history[1, :] = state
    densities[1] = mean(state)
    
    for t in 1:T
        new_state = similar(state)
        
        for i in 1:L
            # Get current site value
            current = state[i]
            
            # Get right neighbor with selected boundary condition  
            if i == L && antiperiodic
                right = 1 - state[1]  # Antiperiodic boundary
            else
                right = state[mod1(i+1, L)]  # Periodic boundary
            end
            
            # Apply conditional probability rules
            if current == 1 && right == 1
                # P(0_j | 1_j, 1_{j+1}) = 1
                new_state[i] = 1
            elseif current == 0 && right == 1
                # P(0_j | 0_j, 1_{j+1}) = 1-q
                # P(1_j | 0_j, 1_{j+1}) = q
                new_state[i] = rand() < (1-q) ? 0 : 1
            elseif current == 1 && right == 0
                # P(0_j | 1_j, 0_{j+1}) = p
                # P(1_j | 1_j, 0_{j+1}) = 1-p
                new_state[i] = rand() < p ? 0 : 1
            else  # current == 0 && right == 0
                # P(0_j | 0_j, 0_{j+1}) = 0
                # P(1_j | 0_j, 0_{j+1}) = 1
                new_state[i] = 0
            end
        end
        
        state = new_state
        @show t, mean(state)
        
        history[t+1, :] = state
        densities[t+1] = mean(state)
    end
    
    return history, densities
end

"""
    visualize_pca_conditional(history, p, q, antiperiodic)
    
Create a visualization of the 1D conditional PCA simulation as a space-time diagram.
"""
function visualize_pca_conditional(history, p, q, antiperiodic)
    T, L = size(history)
    
    # Create folder based on boundary conditions
    folder_name = antiperiodic ? "DATA_PCA_COND_anti" : "DATA_PCA_COND_per"
    
    # Create directory if it doesn't exist
    mkpath(folder_name)
    
    # Create filename with boundary condition info
    filename = "$(folder_name)/spacetime_L$(L)_p$(p)q$(q).png"
    
    # Create space-time diagram
    heatmap(1:L, 1:T, history, 
            c=:grays,
            aspect_ratio=:auto,
            title="1D Conditional PCA - Space-Time Diagram (p=$p, q=$q)",
            xlabel="Position", ylabel="Time",
            legend=false)
    
    savefig(filename)
    return filename
end

"""
    visualize_pca_conditional_animation(history, p, q, antiperiodic)
    
Create an animated visualization of the 1D conditional PCA simulation.
"""
function visualize_pca_conditional_animation(history, p, q, antiperiodic)
    T, L = size(history)
    
    # Create folder based on boundary conditions
    folder_name = antiperiodic ? "DATA_PCA_COND_anti" : "DATA_PCA_COND_per"
    
    # Create directory if it doesn't exist
    mkpath(folder_name)
    
    # Create filename with boundary condition info
    filename = "$(folder_name)/animation_L$(L)_p$(p)q$(q).gif"
    
    anim = @animate for t in 1:T
        plot(1:L, history[t, :], 
             ylim=(-0.1, 1.1),
             marker=:circle,
             markersize=4,
             linewidth=2,
             title="1D Conditional PCA (t=$t, p=$p, q=$q)",
             xlabel="Position", ylabel="State",
             legend=false)
    end
    
    return gif(anim, filename, fps=5)
end

"""
    analyze_density_conditional(densities, L, p, q, antiperiodic)
    
Analyze and plot the density evolution for the 1D conditional PCA.
"""
function analyze_density_conditional(densities, L, p, q, antiperiodic)
    T = length(densities)
    
    # Create folder based on boundary conditions
    folder_name = antiperiodic ? "DATA_PCA_COND_anti" : "DATA_PCA_COND_per"
    
    # Create directory if it doesn't exist
    mkpath(folder_name)
    
    # Create filename with boundary condition info
    filename = "$(folder_name)/density_L$(L)_p$(p)q$(q).png"
    
    plot(1:T, densities, 
         xlabel="Time", ylabel="Density of 1s",
         title="Density Evolution in 1D Conditional PCA (p=$p, q=$q)",
         linewidth=2,
         legend=false)
    
    savefig(filename)
    return densities
end

"""
    print_transition_rules(p, q)
    
Print the complete transition table for the conditional PCA.
"""
function print_transition_rules(p, q)
    println("\n=== Conditional PCA Transition Rules ===")
    println("P(new_state | current, right_neighbor)")
    println()
    println("Current | Right | P(0) | P(1)")
    println("--------|-------|------|------")
    println("   1    |   1   | 0.00 | 1.00")
    println("   0    |   1   | $(1-q) | $q")
    println("   1    |   0   | $p | $(1-p)")
    println("   0    |   0   | 1.00 | 0.00")
    println()
end

"""
    analyze_steady_state_theory(p, q)
    
Theoretical analysis of the steady state density.
"""
function analyze_steady_state_theory(p, q)
    println("\n=== Theoretical Analysis ===")
    println("Parameters: p = $p, q = $q")
    
    # The system has deterministic transitions for some configurations:
    # (1,1) -> 0 always
    # (0,0) -> 1 always
    # Only (0,1) and (1,0) have stochastic transitions
    
    println("\nDeterministic transitions:")
    println("(current=1, right=1) -> new=0 (probability 1)")
    println("(current=0, right=0) -> new=1 (probability 1)")
    
    println("\nStochastic transitions:")
    println("(current=0, right=1) -> new=0 with prob $(1-q), new=1 with prob $q")
    println("(current=1, right=0) -> new=0 with prob $p, new=1 with prob $(1-p)")
    
    # For large systems, we can estimate steady state density
    # This is a rough approximation - exact analysis requires transfer matrix
    if p + q != 1.0
        println("\nNote: For exact steady state analysis, use transfer matrix methods.")
    end
end

# Example simulation run
L = 50  # Length of 1D lattice
T = 5*L  # Number of time steps
p = 0.9  # P(0_j | 1_j, 0_{j+1})
q = 0.9  # 1 - P(0_j | 0_j, 1_{j+1})
antiperiodic = false  # Boundary condition

@show L, T, p, q, antiperiodic

# Print transition rules
print_transition_rules(p, q)

# Theoretical analysis
analyze_steady_state_theory(p, q)

# Run simulation
@time history, densities = simulate_pca_1d_conditional(L, T, p=p, q=q, antiperiodic=antiperiodic)

# Create visualizations
@time spacetime_file = visualize_pca_conditional(history, p, q, antiperiodic)
@time animation_file = visualize_pca_conditional_animation(history, p, q, antiperiodic)
@time density_result = analyze_density_conditional(densities, L, p, q, antiperiodic)

println("Conditional PCA simulation complete!")
println("Space-time diagram saved as: $spacetime_file")
println("Animation saved as: $animation_file")

# Print final statistics
println("\nFinal Statistics:")
println("Initial density: $(round(densities[1], digits=4))")
println("Final density: $(round(densities[end], digits=4))")
println("Average density (last half): $(round(mean(densities[div(T,2):end]), digits=4))")

# Check for steady state
if T > 100
    recent_densities = densities[end-50:end]
    density_variance = var(recent_densities)
    println("Density variance (last 50 steps): $(round(density_variance, digits=6))")
    if density_variance < 1e-4
        println("System appears to have reached steady state.")
    else
        println("System may still be evolving.")
    end
end
