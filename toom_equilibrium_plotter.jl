using Plots

"""
    plot_equilibrium_difference(p, q; resolution=100)

Plot the function f(r) - r where:
f(r) = (1-q)[r^3+3r^2(1-r)] + p[(1-r)^3+3(1-r)^2*r]

Equilibrium points occur where this function equals zero.

Parameters:
- p, q: Error probabilities
- resolution: Number of points to plot
"""
function plot_equilibrium_difference(p, q; resolution=100)
    # Define the function f(r)
    function f(r)
        term1 = (1-q) * (r^3 + 3*r^2*(1-r))
        term2 = p * ((1-r)^3 + 3*(1-r)^2*r)
        return term1 + term2
    end
    
    # Define the difference function f(r) - r
    function diff_function(r)
        return f(r) - r
    end
    
    # Create r values
    r_values = range(0, 1, length=resolution)
    
    # Calculate difference values
    diff_values = [diff_function(r) for r in r_values]
    
    # Plot
    plt = plot(r_values, diff_values, 
               label="f(r) - r", 
               linewidth=2, 
               xlabel="r", 
               ylabel="f(r) - r", 
               title="Equilibrium Function Difference (p=$p, q=$q)",
               legend=:topleft)
    
    # Add horizontal line at y = 0
    hline!([0], label="y = 0", color=:black, linestyle=:dash)
    
    # Add vertical lines at 0 and 1
    vline!([0, 1], label=false, color=:gray, linestyle=:dot)
    
    # Find and mark zero crossings (equilibria)
    equilibria = []
    for i in 1:(resolution-1)
        r1, r2 = r_values[i], r_values[i+1]
        d1, d2 = diff_values[i], diff_values[i+1]
        
        # Check for sign change (indicates crossing)
        if d1 * d2 <= 0 && !(d1 == 0 && d2 == 0)
            # Linear interpolation to find approximate crossing
            t = -d1 / (d2 - d1)
            r_eq = r1 + t * (r2 - r1)
            push!(equilibria, r_eq)
        end
    end
    
    # Mark equilibria on plot
    scatter!(equilibria, zeros(length(equilibria)), 
             label="Equilibria", 
             markersize=6, 
             color=:red)
    
    # Add text labels with equilibrium values
    for (i, r_eq) in enumerate(equilibria)
        annotate!([(r_eq, 0.02, text("r ≈ $(round(r_eq, digits=3))", 8, :bottom))])
    end
    
    return plt
end

# Interactive function to adjust parameters
function interactive_plot()
    println("Enter parameters p and q (both in range [0, 0.5]):")
    print("p = ")
    p = parse(Float64, readline())
    print("q = ")
    q = parse(Float64, readline())
    
    # Validate input
    if !(0 <= p <= 0.5 && 0 <= q <= 0.5)
        println("Error: Parameters must be in range [0, 0.5]")
        return
    end
    
    # Generate plot
    plt = plot_equilibrium_difference(p, q)
    display(plt)
    
    # Save plot
    filename = "toom_difference_p$(p)_q$(q).png"
    savefig(plt, filename)
    println("Plot saved as $filename")
    
    # Ask if user wants to try another set of parameters
    print("Try another set of parameters? (y/n): ")
    response = lowercase(readline())
    if response == "y"
        interactive_plot()
    end
end

# Run the interactive plotter
interactive_plot()