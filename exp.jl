using LinearAlgebra


function get_K(p,q)
    K = zeros(Float64, 2,2,2,2)   # order: out c n e  cout
    for s = 0:1, n = 0:1, e = 0:1, c = 0:1 
        zs = (-1)^s; zn = (-1)^n; ze = (-1)^e; zc = (-1)^c;
        Δ = (zn+ze+zc - zn*ze*zc)/2
        # @show Δ
        K[s+1,c+1,n+1,e+1] = (1+zs*( (p-q) + (1-p-q)*Δ))/2
    end
    return K
end

function get_delta_tensor()
    # Create a 4D tensor with dimensions 2×2×2×2
    delta = zeros(Int8, 2, 2, 2, 2)
    
    # Fill according to delta(i,j,k,l) = δ(i,j) × δ(j,k) × δ(k,l)
    # where δ is the Kronecker delta (1 if indices equal, 0 otherwise)
    for i = 1:2, j = 1:2, k = 1:2, l = 1:2
        # Kronecker deltas: 1 if indices match, 0 otherwise
        delta_ij = (i == j) ? 1 : 0
        delta_jk = (j == k) ? 1 : 0
        delta_kl = (k == l) ? 1 : 0
        
        # Product of all three delta functions
        delta[i,j,k,l] = delta_ij * delta_jk * delta_kl
    end
    
    return delta
end

δ = get_delta_tensor(); δmat = reshape(δ, 8,2)

p = q = 0.1;
# q = 0.00001
K = get_K(p,q); Kmat = reshape(K, 2,8)
Kmat = δmat*Kmat
F = eigen(Kmat); @show F.values
##
# display(Kmat)
xp = [0.5;0.5]; xp = xp/sum(xp)
zp = [1;0.]; zp = zp/sum(zp)
zm = [0; 1];
##
# display(xp'*Kmat)
# vec = [0.95,0.05];
# for i = 1:10
#     vec = Kmat*kron(vec, kron(vec,vec))
#     vec = vec/sum(vec)
#     @show (vec)
# end
# xp = [0.4;0.6]; zp = [0.9,0.1]
init = kron(xp, kron(xp,xp))
vec = Kmat*init; #vec = vec/norm(vec)
@show init
@show (vec)

init = kron(zp, kron(zp,zp))
vec = Kmat*init; #vec = vec/norm(vec)
@show init
@show (vec)

init = kron(zm, kron(zm,zm))
vec = Kmat*init; #vec = vec/norm(vec)
@show init
@show (vec);
##
display(zp'*Kmat*kron(zp, kron(zp,zp)))
# test_x = xp'*Kmat*kron(xp, kron(xp,xp)) / (norm(xp)*norm(Kmat*kron(xp, kron(xp,xp))))
# display(test_x)
# test_z = zp'*Kmat*kron(zp, kron(zp,zp)) / (norm(zp)*norm(Kmat*kron(zp, kron(zp,zp))))
# display(test_z)

#  F = svd(Kmat)