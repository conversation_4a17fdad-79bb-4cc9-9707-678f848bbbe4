using LinearAlgebra
using SparseArrays
using Printf
using Plots

"""
    quantum_ising_1d_hamiltonian(L, h; boundary=:periodic)

Construct the Hamiltonian for the 1D quantum Ising model:
H = -∑_j Z_j Z_{j+1} + h ∑_j X_j

Parameters:
- L: Number of spins (chain length)
- h: Transverse field strength
- boundary: :periodic (Z_{L+1} = Z_1) or :antiperiodic (Z_{L+1} = -Z_1)

Returns:
- H: Sparse Hamiltonian matrix of size 2^L × 2^L
"""
function quantum_ising_1d_hamiltonian(L, h; boundary=:periodic)
    # Pauli matrices
    σx = [0 1; 1 0]
    σz = [1 0; 0 -1]
    I2 = [1 0; 0 1]
    
    # Total Hilbert space dimension
    dim = 2^L
    
    # Initialize Hamiltonian as sparse matrix
    H = spzeros(ComplexF64, dim, dim)
    
    println("Constructing 1D quantum Ising Hamiltonian...")
    println("Chain length: $L")
    println("Transverse field: $h")
    println("Boundary condition: $boundary")
    
    # ZZ interaction terms: -∑_j Z_j Z_{j+1}
    for j in 1:(L-1)
        # Create Z_j ⊗ Z_{j+1} operator
        ZZ_op = 1.0
        for k in 1:L
            if k == j
                ZZ_op = kron(ZZ_op, σz)
            elseif k == j+1
                ZZ_op = kron(ZZ_op, σz)
            else
                ZZ_op = kron(ZZ_op, I2)
            end
        end
        H -= sparse(ZZ_op)
    end
    
    # Boundary term: Z_L Z_1 (with appropriate sign for boundary condition)
    if boundary == :periodic
        # Z_L ⊗ Z_1 (periodic: Z_{L+1} = Z_1)
        ZZ_boundary = 1.0
        for k in 1:L
            if k == 1 || k == L
                ZZ_boundary = kron(ZZ_boundary, σz)
            else
                ZZ_boundary = kron(ZZ_boundary, I2)
            end
        end
        H -= sparse(ZZ_boundary)

    elseif boundary == :antiperiodic
        # -Z_L ⊗ Z_1 (antiperiodic: Z_{L+1} = -Z_1)
        ZZ_boundary = 1.0
        for k in 1:L
            if k == 1 || k == L
                ZZ_boundary = kron(ZZ_boundary, σz)
            else
                ZZ_boundary = kron(ZZ_boundary, I2)
            end
        end
        H += sparse(ZZ_boundary)  # Note the + sign for antiperiodic
    end
    
    # Transverse field terms: h ∑_j X_j
    for j in 1:L
        # Create X_j operator
        X_op = 1.0
        for k in 1:L
            if k == j
                X_op = kron(X_op, σx)
            else
                X_op = kron(X_op, I2)
            end
        end
        H += h * sparse(X_op)
    end
    
    return H
end

"""
    diagonalize_quantum_ising(L, h; boundary=:periodic)

Diagonalize the 1D quantum Ising Hamiltonian and analyze the spectrum.
"""
function diagonalize_quantum_ising(L, h; boundary=:periodic)
    # Construct Hamiltonian
    H = quantum_ising_1d_hamiltonian(L, h, boundary=boundary)
    
    println("\nDiagonalizing Hamiltonian...")
    println("Matrix size: $(size(H))")
    println("Number of non-zero elements: $(nnz(H))")
    
    # Diagonalize
    if size(H, 1) <= 1024  # Practical limit for full diagonalization
        eigenvals, eigenvecs = eigen(Array(H))
        
        # Sort by energy
        idx = sortperm(real(eigenvals))
        eigenvals = eigenvals[idx]
        eigenvecs = eigenvecs[:, idx]
        
        println("\n=== Energy Spectrum ===")
        println("Ground state energy: $(real(eigenvals[1]))")
        println("First excited state energy: $(real(eigenvals[2]))")
        println("Energy gap: $(real(eigenvals[2] - eigenvals[1]))")
        
        # Show lowest energy levels
        println("\nLowest energy levels:")
        for i in 1:min(10, length(eigenvals))
            E = real(eigenvals[i])
            println(@sprintf("E_%d = %12.8f", i-1, E))
        end
        
        return eigenvals, eigenvecs, H
    else
        println("Matrix too large for full diagonalization.")
        println("Consider using iterative methods for larger systems.")
        return nothing, nothing, H
    end
end

"""
    analyze_ground_state(ground_state, L)

Analyze the ground state wavefunction.
"""
function analyze_ground_state(ground_state, L)
    println("\n=== Ground State Analysis ===")
    
    # Helper function to convert index to spin configuration
    function index_to_spins(idx, L)
        idx = idx - 1  # Convert to 0-based
        spins = zeros(Int, L)
        for i in 1:L
            spins[i] = (idx >> (i-1)) & 1 == 0 ? -1 : 1  # 0 -> -1, 1 -> +1
        end
        return spins
    end
    
    # Find configurations with largest amplitudes
    amplitudes = abs.(ground_state).^2
    sorted_indices = sortperm(amplitudes, rev=true)
    
    println("Configurations with largest amplitudes in ground state:")
    for i in 1:min(10, length(sorted_indices))
        idx = sorted_indices[i]
        config = index_to_spins(idx, L)
        amplitude = amplitudes[idx]
        config_str = join([s == 1 ? "↑" : "↓" for s in config], "")
        println(@sprintf("%s: %.6f", config_str, amplitude))
    end
    
    # Calculate magnetization in z-direction
    magnetization_z = 0.0
    for idx in 1:length(ground_state)
        config = index_to_spins(idx, L)
        mag_z = sum(config) / L
        magnetization_z += abs(ground_state[idx])^2 * mag_z
    end
    
    println(@sprintf("\nGround state magnetization ⟨M_z⟩: %.6f", magnetization_z))
    
    return amplitudes
end

"""
    compute_correlation_functions(ground_state, L)

Compute spin-spin correlation functions in the ground state.
"""
function compute_correlation_functions(ground_state, L)
    println("\n=== Correlation Functions ===")
    
    # Helper function to convert index to spin configuration
    function index_to_spins(idx, L)
        idx = idx - 1
        spins = zeros(Int, L)
        for i in 1:L
            spins[i] = (idx >> (i-1)) & 1 == 0 ? -1 : 1
        end
        return spins
    end
    
    # Compute ⟨Z_i Z_j⟩ correlation function
    zz_corr = zeros(Float64, L, L)
    
    for i in 1:L, j in 1:L
        corr_val = 0.0
        for idx in 1:length(ground_state)
            config = index_to_spins(idx, L)
            corr_val += abs(ground_state[idx])^2 * config[i] * config[j]
        end
        zz_corr[i, j] = corr_val
    end
    
    # Display correlation matrix
    println("⟨Z_i Z_j⟩ correlation matrix:")
    for i in 1:L
        row_str = ""
        for j in 1:L
            row_str *= @sprintf("%8.4f ", zz_corr[i, j])
        end
        println("Site $i: $row_str")
    end
    
    # Compute correlation as function of distance
    println("\nCorrelation vs distance:")
    println("Distance | ⟨Z_0 Z_r⟩")
    println("---------|----------")
    
    max_dist = div(L, 2)
    for r in 0:max_dist
        if r == 0
            corr_avg = zz_corr[1, 1]
        else
            corr_sum = 0.0
            count = 0
            for i in 1:L
                j = mod1(i + r, L)
                corr_sum += zz_corr[i, j]
                count += 1
            end
            corr_avg = corr_sum / count
        end
        println(@sprintf("%8d | %9.6f", r, corr_avg))
    end
    
    return zz_corr
end

"""
    phase_diagram_analysis(L_values, h_values; boundary=:periodic)

Analyze the phase diagram by computing ground state properties for different parameters.
"""
function phase_diagram_analysis(L_values, h_values; boundary=:periodic)
    println("\n=== Phase Diagram Analysis ===")
    
    results = Dict()
    
    for L in L_values
        if 2^L > 1024
            println("Skipping L=$L (too large for exact diagonalization)")
            continue
        end
        
        results[L] = Dict()
        
        for h in h_values
            println("Computing L=$L, h=$h...")
            
            eigenvals, eigenvecs, H = diagonalize_quantum_ising(L, h, boundary=boundary)
            
            if eigenvals !== nothing
                ground_energy = real(eigenvals[1])
                gap = real(eigenvals[2] - eigenvals[1])
                
                # Compute ground state magnetization
                ground_state = eigenvecs[:, 1]
                
                function index_to_spins(idx, L)
                    idx = idx - 1
                    spins = zeros(Int, L)
                    for i in 1:L
                        spins[i] = (idx >> (i-1)) & 1 == 0 ? -1 : 1
                    end
                    return spins
                end
                
                magnetization = 0.0
                for idx in 1:length(ground_state)
                    config = index_to_spins(idx, L)
                    mag = abs(sum(config)) / L  # |⟨M_z⟩|
                    magnetization += abs(ground_state[idx])^2 * mag
                end
                
                results[L][h] = Dict(
                    "ground_energy" => ground_energy,
                    "gap" => gap,
                    "magnetization" => magnetization
                )
            end
        end
    end
    
    return results
end

# Example usage and analysis
println("="^60)
println("1D QUANTUM ISING MODEL EXACT DIAGONALIZATION")
println("="^60)

# Parameters
L = 4  # Chain length (keep small for exact diagonalization)
h = 0.0001  # Transverse field strength

# Analyze both boundary conditions
for boundary in [:periodic, :antiperiodic]
    println("\n" * "="^40)
    println("BOUNDARY CONDITION: $(uppercase(string(boundary)))")
    println("="^40)
    
    # Diagonalize
    eigenvals, eigenvecs, H = diagonalize_quantum_ising(L, h, boundary=boundary)
    
    if eigenvals !== nothing
        # Analyze ground state
        ground_state = eigenvecs[:, 1]
        amplitudes = analyze_ground_state(ground_state, L)
        
        # Compute correlations
        zz_corr = compute_correlation_functions(ground_state, L)
    end
end

# Phase diagram analysis (for small systems)
# println("\n" * "="^40)
# println("PHASE DIAGRAM ANALYSIS")
# println("="^40)

# L_values = [4, 6]  # Small systems only
# h_values = [0.1, 0.5, 1.0, 2.0]  # Different field strengths

# results_periodic = phase_diagram_analysis(L_values, h_values, boundary=:periodic)
# results_antiperiodic = phase_diagram_analysis(L_values, h_values, boundary=:antiperiodic)

# println("\nAnalysis complete!")
# println("For larger systems, consider using tensor network methods or quantum Monte Carlo.")
