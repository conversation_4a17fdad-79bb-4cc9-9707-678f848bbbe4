using LinearAlgebra
using .PauliDecomp                # bring the module into scope

# EXAMPLE ─ build *any* 5-qubit (32×32) matrix --------------------
n = 5
N = 2^n
M = zeros(ComplexF64, N, N)

# --- your pattern of 1‘s goes here (illustration below) ---------
M[1, 1] = 1
M[1, 2] = 1
M[1, 3] = 1
M[1, 5] = 1
M[1, 9] = 1
M[1,17] = 1
M[N,16] = 1
M[N,24] = 1
M[N,28] = 1
M[N,30] = 1
M[N,31] = 1
M[N,32] = 1
# … add the interior “single-spike’’ entries exactly as you like …
# ----------------------------------------------------------------

coeffs, orbits = decompose(M)     # full list  c_{μ₁…μₙ}
println("non-zero Pauli strings: ", length(coeffs))
println("translation-orbits     : ", length(orbits))

# pretty-print the first few coefficients
show_coeffs(first(coeffs, 10))
