using Plots
using Statistics
using Random

"""
    simulate_pca_1d_ising(L, T; β=1.0, periodic=true, initial_state=:random)

Simulate a one-dimensional Ising chain using Probabilistic Cellular Automaton (PCA) with alternating update rules.

Update Rules:
- Even time steps (t = 0, 2, 4, ...): s_j(t+1) depends on s_j(t) and s_{j+1}(t)
  P(s_j | s'_j, s'_{j+1}) ∝ exp(β * s_j * (s'_j + s'_{j+1}))
  
- Odd time steps (t = 1, 3, 5, ...): s_j(t+1) depends on s_{j-1}(t) and s_j(t)  
  P(s_j | s'_{j-1}, s'_j) ∝ exp(β * s_j * (s'_{j-1} + s'_j))

Parameters:
- L: length of the 1D lattice
- T: number of time steps
- β: coupling parameter (higher β means stronger coupling)
- periodic: if true, use periodic boundary conditions; if false, use open boundaries
- initial_state: :random, :all_up, :all_down, or :mixed

Returns the full state history for visualization.
"""
function simulate_pca_1d_ising(L, T; β=1.0, periodic=true, initial_state=:random)
    # Initialize state with spins ±1
    if initial_state == :random
        state = rand([-1, 1], L)
    elseif initial_state == :all_up
        state = ones(Int, L)
    elseif initial_state == :all_down
        state = -ones(Int, L)
    elseif initial_state == :mixed
        state = zeros(Int, L)
        # Set left half to +1, right half to -1
        state[1:div(L,2)] .= 1
        state[div(L,2)+1:end] .= -1
    else
        error("Unknown initial_state: $initial_state")
    end
    
    # Store history for visualization
    history = zeros(Int, T+1, L)
    magnetizations = zeros(Float64, T+1)
    
    history[1, :] = state
    magnetizations[1] = mean(state)
    
    for t in 1:T
        new_state = copy(state)
        
        if t % 2 == 1  # Odd time step (t = 1, 3, 5, ...)
            # Update rule: s_j(t+1) depends on s_{j-1}(t) and s_j(t)
            for j in 1:L
                # Get left neighbor
                if j == 1
                    if periodic
                        left_neighbor = state[L]
                    else
                        left_neighbor = 0  # Open boundary
                    end
                else
                    left_neighbor = state[j-1]
                end
                
                center = state[j]
                
                # Calculate probabilities for s_j = +1 and s_j = -1
                prob_up = exp(β * (+1) * (left_neighbor + center))
                prob_down = exp(β * (-1) * (left_neighbor + center))
                
                # Normalize probabilities
                total_prob = prob_up + prob_down
                prob_up /= total_prob
                
                # Sample new state
                new_state[j] = rand() < prob_up ? 1 : -1
            end
            
        else  # Even time step (t = 2, 4, 6, ...)
            # Update rule: s_j(t+1) depends on s_j(t) and s_{j+1}(t)
            for j in 1:L
                center = state[j]
                
                # Get right neighbor
                if j == L
                    if periodic
                        right_neighbor = state[1]
                    else
                        right_neighbor = 0  # Open boundary
                    end
                else
                    right_neighbor = state[j+1]
                end
                
                # Calculate probabilities for s_j = +1 and s_j = -1
                prob_up = exp(β * (+1) * (center + right_neighbor))
                prob_down = exp(β * (-1) * (center + right_neighbor))
                
                # Normalize probabilities
                total_prob = prob_up + prob_down
                prob_up /= total_prob
                
                # Sample new state
                new_state[j] = rand() < prob_up ? 1 : -1
            end
        end
        
        state = new_state
        @show t, mean(state)
        
        history[t+1, :] = state
        magnetizations[t+1] = mean(state)
    end
    
    return history, magnetizations
end


"""
    visualize_pca_ising(history, β, periodic)
    
Create a space-time visualization of the PCA Ising simulation.
"""
function visualize_pca_ising(history, β, periodic)
    T, L = size(history)
    
    # Create folder based on boundary conditions
    folder_name = periodic ? "DATA_PCA_periodic" : "DATA_PCA_open"
    
    # Create directory if it doesn't exist
    mkpath(folder_name)
    
    # Create filename
    filename = "$(folder_name)/spacetime_L$(L)_beta$(β).png"
    
    # Create space-time diagram (map -1,1 to 0,1 for visualization)
    plot_data = (history .+ 1) ./ 2
    
    heatmap(1:L, 1:T, plot_data,
            aspect_ratio=:auto,
            title="PCA 1D Ising Model - Space-Time (β=$β)",
            xlabel="Position", ylabel="Time",
            legend=false)
    
    savefig(filename)
    return filename
end

"""
    visualize_pca_ising_animation(history, β, periodic)
    
Create an animated visualization of the PCA Ising simulation.
"""
function visualize_pca_ising_animation(history, β, periodic)
    T, L = size(history)
    
    # Create folder based on boundary conditions
    folder_name = periodic ? "DATA_PCA_periodic" : "DATA_PCA_open"
    
    # Create directory if it doesn't exist
    mkpath(folder_name)
    
    # Create filename
    filename = "$(folder_name)/animation_L$(L)_beta$(β).gif"
    
    anim = @animate for t in 1:T
        plot(1:L, history[t, :], 
             ylim=(-1.2, 1.2),
             marker=:circle,
             markersize=4,
             linewidth=2,
             title="PCA 1D Ising Model (t=$t, β=$β)",
             xlabel="Position", ylabel="Spin",
             legend=false,
             color=:blue)
        hline!([0], color=:gray, linestyle=:dash, alpha=0.5)
    end
    
    return gif(anim, filename, fps=5)
end

"""
    analyze_magnetization(magnetizations, L, β, periodic)
    
Analyze and plot the magnetization evolution.
"""
function analyze_magnetization(magnetizations, L, β, periodic)
    T = length(magnetizations)
    
    # Create folder based on boundary conditions
    folder_name = periodic ? "DATA_PCA_periodic" : "DATA_PCA_open"
    
    # Create directory if it doesn't exist
    mkpath(folder_name)
    
    # Magnetization plot
    mag_filename = "$(folder_name)/magnetization_L$(L)_beta$(β).png"
    plot(1:T, magnetizations, 
         xlabel="Time", ylabel="Magnetization",
         title="Magnetization Evolution (β=$β)",
         linewidth=2,
         legend=false)
    savefig(mag_filename)
    
    return magnetizations
end

# Example simulation run
L = 40  # Length of 1D lattice
T = 2000  # Number of time steps
β = 2.0 # Coupling parameter \beta_c = log(1+√2)/2
periodic = true  # Boundary condition
initial_state = :random  # Initial configuration

# Run simulation
@time history, magnetizations = simulate_pca_1d_ising(L, T, β=β, periodic=periodic, initial_state=initial_state)

# Create visualizations
@time spacetime_file = visualize_pca_ising(history, β, periodic)
# @time animation_file = visualize_pca_ising_animation(history, β, periodic)
@time mag_result = analyze_magnetization(magnetizations, L, β, periodic)

println("PCA Ising simulation complete!")
println("Space-time diagram saved as: $spacetime_file")
# println("Animation saved as: $animation_file")

# Print final statistics
@show L, T, β, periodic, initial_state

println("\nFinal Statistics:")
println("Final magnetization: $(round(magnetizations[end], digits=4))")
println("Average magnetization: $(round(mean(magnetizations[div(T,2):end]), digits=4))")
