using Plots
using Roots
using LinearAlgebra

"""
    equilibrium_equation(r, p, q)

The equilibrium equation for <PERSON><PERSON>'s NEC model:
r = (1-q)[r^3 + 3r^2(1-r)] + p[(1-r)^3 + 3(1-r)^2*r]

Returns the difference between LHS and RHS, so roots are where this equals zero.
"""
function equilibrium_equation(r, p, q)
    term1 = (1-q) * (r^3 + 3*r^2*(1-r))
    term2 = p * ((1-r)^3 + 3*(1-r)^2*r)
    return r - (term1 + term2)
end

"""
    find_all_equilibria(p, q)

Find all solutions r ∈ [0,1] for the equilibrium equation at given p and q.
Returns an array of solutions (may contain 1-3 values).
"""
function find_all_equilibria(p, q)
    # Define the function for this p,q pair
    f(r) = equilibrium_equation(r, p, q)
    
    # Check if there are multiple solutions by examining the derivative
    # at several points
    test_points = range(0.01, 0.99, length=20)
    sign_changes = 0
    prev_sign = sign(f(test_points[1]))
    
    for r in test_points[2:end]
        current_sign = sign(f(r))
        if current_sign != prev_sign && abs(f(r)) < 0.1
            sign_changes += 1
            prev_sign = current_sign
        end
    end
    
    # Try to find all solutions
    solutions = Float64[]
    
    # Always check endpoints
    if abs(f(0)) < 1e-10
        push!(solutions, 0.0)
    end
    if abs(f(1)) < 1e-10
        push!(solutions, 1.0)
    end
    
    # If we expect multiple solutions, use multiple starting points
    if sign_changes > 0
        starting_points = [0.1, 0.5, 0.9]
        for start in starting_points
            try
                sol = find_zero(f, start, Order1())
                if 0 <= sol <= 1 && abs(f(sol)) < 1e-10
                    # Check if this solution is already found (within tolerance)
                    if !any(abs.(solutions .- sol) .< 1e-6)
                        push!(solutions, sol)
                    end
                end
            catch
                # If find_zero fails, just continue
            end
        end
    else
        # If we expect a single solution, try from the middle
        try
            sol = find_zero(f, 0.5, Order1())
            if 0 <= sol <= 1 && abs(f(sol)) < 1e-10
                push!(solutions, sol)
            end
        catch
            # If find_zero fails, just continue
        end
    end
    
    return sort(solutions)
end

"""
    generate_bifurcation_diagram(resolution=100)

Generate a bifurcation diagram showing all values of r as a function of p
for different fixed values of q.
"""
function generate_bifurcation_diagram(resolution=100)
    p_values = range(0, 0.5, length=resolution)
    q_values = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5]
    
    plt = plot(xlabel="p", ylabel="r", title="Equilibrium Solutions", 
               legend=:bottomright, size=(800, 600))
    
    for q in q_values
        # For each q, collect all solutions across p values
        all_r_values = []
        all_p_values = []
        
        for p in p_values
            solutions = find_all_equilibria(p, q)
            append!(all_r_values, solutions)
            append!(all_p_values, fill(p, length(solutions)))
        end
        
        # Plot all solutions for this q value
        scatter!(plt, all_p_values, all_r_values, 
                label="q = $q", markersize=2, alpha=0.7)
    end
    
    savefig(plt, "toom_bifurcation_diagram.png")
    return plt
end

"""
    generate_phase_diagram(resolution=50)

Generate a phase diagram showing the number of equilibria as a function of p and q.
"""
function generate_phase_diagram(resolution=50)
    p_values = range(0, 0.5, length=resolution)
    q_values = range(0, 0.5, length=resolution)
    
    # Count number of solutions for each (p,q) pair
    solution_counts = zeros(Int, resolution, resolution)
    
    for (i, p) in enumerate(p_values)
        for (j, q) in enumerate(q_values)
            solutions = find_all_equilibria(p, q)
            solution_counts[i, j] = length(solutions)
        end
    end
    
    # Create heatmap
    plt = heatmap(p_values, q_values, transpose(solution_counts),
            xlabel="p", ylabel="q", title="Number of Equilibrium Solutions",
            color=cgrad([:blue, :green, :red]), clims=(1,3),
            colorbar_title="Number of solutions")
    
    savefig(plt, "toom_phase_diagram_solutions.png")
    return plt
end

# Run the analysis
println("Generating bifurcation diagram...")
generate_bifurcation_diagram(100)

println("Generating phase diagram...")
generate_phase_diagram(50)

println("Analysis complete!")