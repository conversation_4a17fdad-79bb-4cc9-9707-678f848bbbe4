########################################################################
# NEC 3-D Tensor-Network (binary spins) using TensorOperations.jl
########################################################################
using TensorOperations    # @tensor macro for Einstein summation

    ########## 1.  Helper functions ########################################
    
    """majority(n,e,c) – return 1 if at least two of (n,e,c) are 1, else 0"""
    majority(n,e,c) = (n & e) | (n & c) | (e & c)
    
    """gate_tensor(p,q) – 4-array T[n+1,e+1,c+1,c′+1]"""
    function gate_tensor(p,q)
        T = zeros(Float64, 2,2,2,2)   # order: n e c  cout
        for n in 0:1, e in 0:1, c in 0:1
            M  = majority(n,e,c)
            θ  = p*M + q*(1-M)
            T[n+1,e+1,c+1,M+1]     = 1-θ   # stays majority
            T[n+1,e+1,c+1,1-M+1]   = θ     # flips
        end
        return T
    end
    
    """copy_tensor() – Δ[a+1,b+1,c+1]"""
    function copy_tensor()
        Δ = zeros(Float64, 2,2,2)
        for s in 0:1
            Δ[s+1,s+1,s+1] = 1.0
        end
        return Δ
    end
    
    ########## 2.  Build a 3-D brick of tensors ############################
    
    """
        build_brick(Lx,Ly,Lt; p, q)
    
    Return two Array-of-Arrays:
      centers[x,y,t]  : copy tensors  (Δ)   for t = 1…Lt+1
      gates[x,y,t]    : gate tensors  (T)   for t = 1…Lt
    Indices are stored explicitly so that the same *physical* spin
    leg is shared between tensors at different coordinates.
    """
    function build_brick(Lx,Ly,Lt; p=0.1, q=0.1)
        # Allocate tensors
        centers = [copy_tensor() for _ in 1:Lx, _ in 1:Ly, _ in 1:(Lt+1)]
        gates   = [gate_tensor(p,q) for _ in 1:Lx, _ in 1:Ly, _ in 1:Lt]
    
        # Replace generic legs by actual Index objects (1-dim arrays act as indices)
        # We store the spin legs themselves (length-2 vectors) so they can be shared.
        # Each spin leg is a Vector{Int} of length 2 acting as a trivial basis.
        spinleg(t,x,y) = zeros(Int, 2) + 1*(t+x+y) # dummy unique object
    
        # 1.  Attach physical legs to copy tensors
        for t in 1:(Lt+1), x in 1:Lx, y in 1:Ly
            Δ = centers[x,y,t]
            σ = spinleg(t-1,x,y)
            # axes: (a,b,c) – we replace 'c' with shared physical leg
            Δ[:,:,1] .= Δ[:,:,1]         # keep (a,b)
            Δ[:,:,2] .= 0                # clear old
            Δ[:,:,2] = Δ[:,:,1]          # identical slice; leg sharing is conceptual
            centers[x,y,t] = Δ
        end
    
        # 2.  Gate tensors already fine; sharing is conceptual in this dense array demo
        return centers, gates
    end
    
    ########## 3.  Layer-by-layer contraction (verify Z = 1) ###############
    
    """
        check_partition(Lx,Ly,Lt; p, q) -> Float64
    
    Contracts the whole network; should always return ≈ 1.0
    """
    function check_partition(Lx,Ly,Lt; p=0.1, q=0.05)
        centers, gates = build_brick(Lx,Ly,Lt; p=p,q=q)
        Δ = copy_tensor()                    # reuse fresh Δ for identity
    
        # Iterate from top layer down
        for t in Lt:-1:1
            for x in 1:Lx, y in 1:Ly
                T = gates[x,y,t]
                # Contract gate with Δ above it; only (cout) index is shared.
                @tensor tmp[a,b,c] := T[a,b,c,d] * Δ[d,e,f]   # d is cout
                # Replace copy tensor one layer below by the result
                centers[x,y,t] = tmp
            end
        end
    
        # After collapsing all gates, each copy tensor at t=0 sums to 1
        Z = 1.0
        for x in 1:Lx, y in 1:Ly
            Z *= sum(centers[x,y,1])
        end
        return Z
    end
    
    ########## 4.  Quick test ##############################################
    Lx, Ly, Lt = 3, 3, 4
    @show check_partition(Lx,Ly,Lt; p=0.25, q=0.11)   # → 1.0 (within FP error)
    ########################################################################
    