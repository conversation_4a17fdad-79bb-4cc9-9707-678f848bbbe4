using LinearAlgebra, Statistics
using SparseArrays
using Printf

"""
    create_anchored_transition_matrix(L, λ)

Create the transition matrix for the anchored Monte Carlo dynamics on a 1D spin chain.

Algorithm:
1. Pick site n uniformly at random (probability 1/L each)
2. If σ_n = +:
   - With probability λ: swap with first opposite spin to the right, or flip if none exists
   - With probability 1-λ: do nothing
3. If σ_n = -:
   - With probability 1: swap with first opposite spin to the right, or flip if none exists

Parameters:
- L: Length of the spin chain
- λ: Probability parameter (0 ≤ λ ≤ 1)

Returns:
- P: Sparse transition matrix of size 2^L × 2^L
"""
function create_anchored_transition_matrix(L, λ)
    # Total number of configurations
    n_configs = 2^L
    
    # Use sparse matrix for efficiency
    P = spzeros(Float64, n_configs, n_configs)
    
    # Helper function to convert configuration array to index
    # Maps spins {-1, +1} to bits {1, 0} for indexing
    function config_to_index(config)
        idx = 1
        for i in 1:L
            bit = (1 - config[i]) ÷ 2  # Convert -1,+1 to 1,0
            idx += bit * 2^(i-1)
        end
        return idx
    end

    # Helper function to convert index to configuration array
    function index_to_config(idx)
        idx = idx - 1  # Adjust for 1-based indexing
        config = zeros(Int, L)
        for i in 1:L
            bit = (idx >> (i-1)) & 1
            config[i] = 1 - 2 * bit  # Convert 1,0 to -1,+1
        end
        return config
    end
    
    # Helper function to find first opposite spin to the right of site n
    function find_r(n, config)
        target_spin = -config[n]  # Opposite of current spin
        for j in (n+1):L
            if config[j] == target_spin
                return j
            end
        end
        return nothing  # No opposite spin found
    end
    
    # Helper function to perform swap operation
    function swap_spins(config, n, r)
        new_config = copy(config)
        new_config[n], new_config[r] = new_config[r], new_config[n]
        return new_config
    end
    
    # Helper function to perform flip operation
    function flip_spin(config, n)
        new_config = copy(config)
        new_config[n] = -new_config[n]
        return new_config
    end
    
    println("Building anchored transition matrix for L=$L, λ=$λ...")
    
    # Build the transition matrix
    for idx_from in 1:n_configs
        config_from = index_to_config(idx_from)
        
        # For each possible site selection n (with probability 1/L)
        for n in 1:L
            site_prob = 1.0 / L  # Probability of selecting site n
            
            if config_from[n] == 1  # σ_n = +
                r_n = find_r(n, config_from)
                
                if r_n !== nothing  # r(n) ≠ ∅
                    # Case 1: Swap with probability λ
                    config_swap = swap_spins(config_from, n, r_n)
                    idx_swap = config_to_index(config_swap)
                    P[idx_swap, idx_from] += site_prob * λ
                    
                    # Case 2: Do nothing with probability 1-λ
                    P[idx_from, idx_from] += site_prob * (1 - λ)
                    
                else  # r(n) = ∅
                    # Case 3: Flip with probability λ
                    config_flip = flip_spin(config_from, n)
                    idx_flip = config_to_index(config_flip)
                    P[idx_flip, idx_from] += site_prob * λ
                    
                    # Case 4: Do nothing with probability 1-λ
                    P[idx_from, idx_from] += site_prob * (1 - λ)
                end
                
            else  # σ_n = -
                r_n = find_r(n, config_from)
                
                if r_n !== nothing  # r(n) ≠ ∅
                    # Case 5: Swap with probability 1
                    config_swap = swap_spins(config_from, n, r_n)
                    idx_swap = config_to_index(config_swap)
                    P[idx_swap, idx_from] += site_prob * 1.0
                    
                else  # r(n) = ∅
                    # Case 6: Flip with probability 1
                    config_flip = flip_spin(config_from, n)
                    idx_flip = config_to_index(config_flip)
                    P[idx_flip, idx_from] += site_prob * 1.0
                end
            end
        end
    end
    
    return P
end

"""
    verify_transition_matrix(P)

Verify that P is a proper stochastic matrix (rows sum to 1).
"""
function verify_transition_matrix(P)
    n = size(P, 1)
    cloumn_sums = [sum(P[:, i]) for i in 1:n]
    
    max_error = maximum(abs.(cloumn_sums .- 1.0))
    println("Maximum column sum error: $max_error")
    
    if max_error < 1e-12
        println("✓ Transition matrix is properly normalized")
        return true
    else
        println("✗ Transition matrix normalization error!")
        return false
    end
end

"""
    analyze_anchored_dynamics(P, L)

Analyze the transition matrix to find steady states and relaxation properties.
"""
function analyze_anchored_dynamics(P, L)
    println("\n=== Analyzing Anchored Dynamics ===")
    
    if size(P, 1) <= 1024  # Practical limit for eigendecomposition
        # Find eigenvalues and eigenvectors
        F = eigen(Array(P))  # Transpose for left eigenvectors
        eigenvalues = F.values
        eigenvectors = F.vectors
        
        # Sort by magnitude of eigenvalues
        idx = sortperm(abs.(eigenvalues), rev=true)
        eigenvalues = eigenvalues[idx]
        eigenvectors = eigenvectors[:, idx]
        println("Largest eigenvalues (by magnitude):")
        for i in 1:min(8, length(eigenvalues))
            λ = eigenvalues[i]
            println(@sprintf("λ%d = %8.6f + %8.6fi (|λ| = %8.6f)", 
                    i, real(λ), imag(λ), abs(λ)))
        end
        println("fixed-point probability:")
        fix = eigenvectors[:,1]/sum(eigenvectors[:,1])
        display(fix)

        
        # Find steady states (eigenvalue ≈ 1)
        steady_indices = findall(x -> abs(abs(x) - 1.0) < 1e-10, eigenvalues)
        
        if !isempty(steady_indices)
            println("\nFound $(length(steady_indices)) steady state(s)")
            
            # Analyze the dominant steady state
            steady_state = eigenvectors[:, steady_indices[1]]
            steady_state = abs.(steady_state) ./ sum(abs.(steady_state))
            
            # Helper function to convert index to configuration
            function index_to_config(idx)
                idx = idx - 1
                config = zeros(Int, L)
                for i in 1:L
                    bit = (idx >> (i-1)) & 1
                    config[i] = 1 - 2 * bit  # Convert 1,0 to -1,+1
                end
                return config
            end
            
            # Show most probable configurations
            println("\nMost probable configurations in steady state:")
            sorted_indices = sortperm(steady_state, rev=true)
            
            for i in 1:min(10, length(sorted_indices))
                idx = sorted_indices[i]
                config = index_to_config(idx)
                prob = steady_state[idx]
                config_str = join([s == 1 ? "+" : "-" for s in config], "")
                println(@sprintf("%s -> %.6f", config_str, prob))
            end
            
            # Compute average magnetization
            avg_magnetization = 0.0
            for idx in 1:length(steady_state)
                config = index_to_config(idx)
                magnetization = mean(config)
                avg_magnetization += steady_state[idx] * magnetization
            end
            println(@sprintf("\nAverage magnetization in steady state: %.6f", avg_magnetization))
            
            # Relaxation time from second largest eigenvalue
            if length(eigenvalues) >= 2
                λ2 = eigenvalues[2]
                if abs(λ2) > 1e-10
                    relaxation_time = -1.0 / log(abs(λ2))
                    println(@sprintf("Relaxation time: %.4f", relaxation_time))
                end
            end
        else
            println("\nNo steady states found with |λ| ≈ 1")
        end
        
        return Dict(
            "eigenvalues" => eigenvalues,
            "eigenvectors" => eigenvectors,
            "steady_indices" => steady_indices
        )
    else
        println("Matrix too large for eigendecomposition.")
        return P
    end
end

# Example usage
L = 4  # Small chain for demonstration
λ = 1  # Probability parameter

println("Creating anchored transition matrix for L=$L, λ=$λ...")

# Create transition matrix
P = create_anchored_transition_matrix(L, λ)

println("Transition matrix size: $(size(P))")
println("Number of non-zero elements: $(nnz(P))")
display(P)

# Verify the matrix is properly normalized
verify_transition_matrix(P)

# Analyze the dynamics
results = analyze_anchored_dynamics(P, L)

println("\nAnalysis complete!")
