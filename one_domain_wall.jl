##############################################################################
#  <Zj>, <Zj Zk> and Var(M) for   |Ψ(β)〉  with arbitrary bond couplings βj   #
#  antiperiodic boundary:  Z_{L+1} = − Z₁                                    #
##############################################################################
using LinearAlgebra                      # only for good BLAS printing

"""
    single_domain_wall_expectations(β::AbstractVector)

Return three items

1.  `Zexp[j]   = ⟨Z_j⟩`
2.  `ZZ[j,k]   = ⟨Z_j Z_k⟩`
3.  `varM      = Var(M)  with  M = Σ_j Z_j`

for the inhomogeneous chain described by a vector of bond–couplings
`β[1:L]` (length `L`).  
Algorithm uses the closed-form weights derived analytically:

      w_m = 2cosh(2β_m)         (orientation–summed weight of bond m)
      Δ_m = 2sinh(2β_m)         (oriented weight of bond m)
"""
function single_domain_wall_expectations(β::AbstractVector)
    L = length(β)
    w = 2 .* cosh.(2 .* β)                  # wₘ
    Δ = 2 .* sinh.(2 .* β)                  # Δₘ
    W = sum(w)                              # normaliser  Σ wₘ
    S = sum(Δ)                              # Σ Δₘ

    # ---------- ⟨Z_j⟩ -------------------------------------------------------
    Zexp = zeros(Float64, L)
    prefixΔ = cumsum(Δ)                     # P_j  (with P₀ ≡ 0)
    for j in 1:L
        Pjm1 = j == 1 ? 0.0 : prefixΔ[j-1]
        Zexp[j] = (S - 2Pjm1) / W
    end

    # ---------- ⟨Z_j Z_k⟩ ---------------------------------------------------
    ZZ = ones(Float64, L, L)                # start with r = 0 diagonal
    for j in 1:L
        for r in 1:L-1                      # separation 1…L−1
            # total weight of the r-bond arc starting at bond j
            sum_arc = 0.0
            for k in 0:r-1
                m = mod(j + k - 1, L) + 1   # bond index (1-based, periodic)
                sum_arc += w[m]
            end
            ε = (j + r ≤ L) ? 1.0 : -1.0    # extra sign from anti-periodic twist
            ZZ[j, mod(j + r - 1, L) + 1] = ε * (W - 2sum_arc) / W
        end
    end

    # ---------- Var(M) ------------------------------------------------------
    meanM = sum(Zexp)
    M2    = sum(ZZ)                         # Σ_{j,k} ⟨Z_j Z_k⟩
    varM  = M2 - meanM^2

    return Zexp, ZZ, varM
end

# β = [0.6, -0.4, 0.6, -0.4, 0.6, -0.4]       # example: alternating a/b,  L = 6
L = 20
β = ones(L)           # example: all a,  L = 20
@time Zexp, ZZ, varM = single_domain_wall_expectations(β)

println("⟨Z_j⟩ = ", Zexp)                   # vector length L
println("⟨Z_j Z_k⟩ matrix:")
display(ZZ)
println("Var(M) = ", varM/L)
