using LinearAlgebra
using SparseArrays
using Printf
using Statistics

⊗(A,B) = kron(A,B)

"""
    create_pca_conditional_transfer_matrix(L, p, q; antiperiodic=false)

Create the transfer matrix for the 1D conditional PCA with rules:
- P(0_j | 0_j, 0_{j+1}) = 1
- P(0_j | 0_j, 1_{j+1}) = 1-q  
- P(0_j | 1_j, 0_{j+1}) = p
- P(0_j | 1_j, 1_{j+1}) = 0

Parameters:
- L: Length of the 1D lattice
- p: Probability P(0_j | 1_j, 0_{j+1})
- q: Probability P(1_j | 0_j, 1_{j+1}) = 1 - P(0_j | 0_j, 1_{j+1})
- antiperiodic: Boundary condition (true for antiperiodic, false for periodic)

Returns a sparse matrix representing the transfer operator.
"""
function create_pca_conditional_transfer_matrix(L, p, q; antiperiodic=false)
    # Total number of states
    n_states = 2^L
    
    # Use sparse matrix for efficiency
    T = spzeros(Float64, n_states, n_states)
    
    # Helper function to convert state array to index
    function state_to_index(state)
        idx = 1
        for i in 1:L
            idx += state[i] * 2^(i-1)
        end
        return idx
    end
    
    # Helper function to convert index to state array
    function index_to_state(idx)
        idx = idx - 1  # Adjust for 1-based indexing
        state = zeros(Int, L)
        for i in 1:L
            state[i] = (idx >> (i-1)) & 1
        end
        return state
    end
    
    # Build the transfer matrix
    println("Building conditional PCA transfer matrix...")
    println("Parameters: L=$L, p=$p, q=$q, antiperiodic=$antiperiodic")
    
    if L <= 20  # Practical for systems up to length 20
        for idx_from in 1:n_states
            state_from = index_to_state(idx_from)
            
            # Calculate all possible next states and their probabilities
            for config in 0:(2^L-1)
                state_to = zeros(Int, L)
                total_prob = 1.0
                
                # Convert configuration to state
                for i in 1:L
                    state_to[i] = (config >> (i-1)) & 1
                end
                
                # Calculate transition probability for this configuration
                for i in 1:L
                    current = state_from[i]
                    
                    # Get right neighbor with selected boundary condition
                    if i == L && antiperiodic
                        right = 1 - state_from[1]  # Antiperiodic boundary
                    else
                        right = state_from[mod1(i+1, L)]  # Periodic boundary
                    end
                    
                    # Apply conditional probability rules
                    if current == 0 && right == 0
                        # P(0_j | 0_j, 0_{j+1}) = 1, P(1_j | 0_j, 0_{j+1}) = 0
                        if state_to[i] == 0
                            total_prob *= 1.0
                        else
                            total_prob *= 0.0
                        end
                    elseif current == 0 && right == 1
                        # P(0_j | 0_j, 1_{j+1}) = 1-q, P(1_j | 0_j, 1_{j+1}) = q
                        if state_to[i] == 0
                            total_prob *= (1.0 - q)
                        else
                            total_prob *= q
                        end
                    elseif current == 1 && right == 0
                        # P(0_j | 1_j, 0_{j+1}) = p, P(1_j | 1_j, 0_{j+1}) = 1-p
                        if state_to[i] == 0
                            total_prob *= p
                        else
                            total_prob *= (1.0 - p)
                        end
                    else  # current == 1 && right == 1
                        # P(0_j | 1_j, 1_{j+1}) = 0, P(1_j | 1_j, 1_{j+1}) = 1
                        if state_to[i] == 0
                            total_prob *= 0.0
                        else
                            total_prob *= 1.0
                        end
                    end
                end
                
                # Add to transfer matrix
                idx_to = state_to_index(state_to)
                T[idx_to, idx_from] = total_prob
            end
        end
    else
        # For larger systems, use Monte Carlo sampling
        println("System too large for exact matrix. Using Monte Carlo sampling...")
        
        n_samples = 10000
        
        for idx_from in 1:min(n_states, 50000)
            state_from = index_to_state(idx_from)
            
            # Sample next states
            for _ in 1:n_samples
                new_state = similar(state_from)
                
                for i in 1:L
                    current = state_from[i]
                    
                    # Get right neighbor with selected boundary condition
                    if i == L && antiperiodic
                        right = 1 - state_from[1]  # Antiperiodic boundary
                    else
                        right = state_from[mod1(i+1, L)]  # Periodic boundary
                    end
                    
                    # Apply conditional probability rules
                    if current == 0 && right == 0
                        # P(0_j | 0_j, 0_{j+1}) = 1
                        new_state[i] = 0
                    elseif current == 0 && right == 1
                        # P(0_j | 0_j, 1_{j+1}) = 1-q, P(1_j | 0_j, 1_{j+1}) = q
                        new_state[i] = rand() < (1-q) ? 0 : 1
                    elseif current == 1 && right == 0
                        # P(0_j | 1_j, 0_{j+1}) = p, P(1_j | 1_j, 0_{j+1}) = 1-p
                        new_state[i] = rand() < p ? 0 : 1
                    else  # current == 1 && right == 1
                        # P(0_j | 1_j, 1_{j+1}) = 0, P(1_j | 1_j, 1_{j+1}) = 1
                        new_state[i] = 1
                    end
                end
                
                # Add to transfer matrix
                idx_to = state_to_index(new_state)
                T[idx_to, idx_from] += 1.0 / n_samples
            end
        end
    end
    
    return T
end

"""
    analyze_pca_conditional_transfer_matrix(T; matrix_name="Conditional PCA Transfer Matrix")

Analyze the conditional PCA transfer matrix to find steady states and relaxation times.
"""
function analyze_pca_conditional_transfer_matrix(T; matrix_name="Conditional PCA Transfer Matrix")
    println("Analyzing $matrix_name...")
    
    if size(T, 1) <= 2^16  # Practical limit for eigendecomposition
        # Right eigendecomposition
        F_right = eigen(Array(T))
        eigenvalues_right = F_right.values
        eigenvectors_right = F_right.vectors
        
        # Sort by magnitude of eigenvalues
        idx_right = sortperm(abs.(eigenvalues_right), rev=true)
        eigenvalues_right = eigenvalues_right[idx_right]
        eigenvectors_right = eigenvectors_right[:, idx_right]
        display(eigenvectors_right[:,1:min(2, size(eigenvectors_right, 2))])
        println("\n=== $matrix_name Analysis ===")
        println("Largest eigenvalues (by magnitude):")
        for i in 1:min(8, length(eigenvalues_right))
            λ = eigenvalues_right[i]
            println(@sprintf("λ%d = %8.6f + %8.6fi (|λ| = %8.6f)", 
                    i, real(λ), imag(λ), abs(λ)))
        end
        
        # Find steady states (eigenvalue ≈ 1)
        steady_indices = findall(x -> abs(abs(x) - 1.0) < 1e-10, eigenvalues_right)
        
        if !isempty(steady_indices)
            println("\nFound $(length(steady_indices)) steady state(s):")
            for (i, idx) in enumerate(steady_indices)
                λ = eigenvalues_right[idx]
                println(@sprintf("  Steady state %d: λ = %8.6f + %8.6fi", i, real(λ), imag(λ)))
            end
            
            # Relaxation time from second largest eigenvalue
            if length(eigenvalues_right) >= 2
                λ2 = eigenvalues_right[2]
                if abs(λ2) > 1e-10
                    relaxation_time = -1.0 / log(abs(λ2))
                    println(@sprintf("Relaxation time: %.4f", relaxation_time))
                end
            end
        else
            println("\nNo steady states found with |λ| ≈ 1")
        end
        
        return Dict(
            "eigenvalues" => eigenvalues_right,
            "eigenvectors" => eigenvectors_right,
            "steady_indices" => steady_indices
        )
    else
        println("Matrix too large for eigendecomposition.")
        return T
    end
end

"""
    compute_conditional_pca_correlations(steady_state, L)

Compute the two-point correlation function <s_i s_j> for the conditional PCA steady state.
"""
function compute_conditional_pca_correlations(steady_state, L)
    n_states = length(steady_state)
    
    # Helper function to convert index to state array
    function index_to_state(idx)
        idx = idx - 1
        state = zeros(Int, L)
        for i in 1:L
            state[i] = (idx >> (i-1)) & 1
        end
        return state
    end
    
    # Normalize steady state to ensure it's a probability distribution
    steady_state = abs.(steady_state) ./ sum(abs.(steady_state))
    
    # Compute single-site expectation values <s_i>
    single_site_avg = zeros(Float64, L)
    for i in 1:L
        for state_idx in 1:n_states
            state = index_to_state(state_idx)
            single_site_avg[i] += steady_state[state_idx] * state[i]
        end
    end
    
    # Compute two-point expectation values <s_i s_j>
    two_point_avg = zeros(Float64, L, L)
    for i in 1:L, j in 1:L
        for state_idx in 1:n_states
            state = index_to_state(state_idx)
            two_point_avg[i, j] += steady_state[state_idx] * state[i] * state[j]
        end
    end
    
    # Compute correlation function: <s_i s_j> - <s_i><s_j>
    correlation_matrix = zeros(Float64, L, L)
    for i in 1:L, j in 1:L
        correlation_matrix[i, j] = two_point_avg[i, j] - single_site_avg[i] * single_site_avg[j]
    end
    
    mean_density = mean(single_site_avg)
    
    return correlation_matrix, mean_density, single_site_avg, two_point_avg
end

"""
    display_most_probable_configs(steady_state, L; num_configs=10)

Display the most probable configurations in the steady state.
"""
function display_most_probable_configs(steady_state, L; num_configs=2*L+1)
    # Helper function to convert index to state array
    function index_to_state(idx)
        idx = idx - 1
        state = zeros(Int, L)
        for i in 1:L
            state[i] = (idx >> (i-1)) & 1
        end
        return state
    end
    
    # Normalize steady state
    steady_state = abs.(steady_state) ./ sum(abs.(steady_state))
    
    # Find most probable configurations
    sorted_indices = sortperm(steady_state, rev=true)
    
    println("\nMost probable configurations in steady state:")
    for i in 1:min(num_configs, length(sorted_indices))
        idx = sorted_indices[i]
        config = index_to_state(idx)
        prob = steady_state[idx]
        config_str = join(config, "")
        println(@sprintf("%s -> %.6f", config_str, prob))
    end
end

# Example usage
L = 11  # Small lattice for demonstration
p = 0.6  # P(0_j | 1_j, 0_{j+1})
q = 0.6  # P(1_j | 0_j, 1_{j+1})
antiperiodic = true

println("Creating conditional PCA transfer matrix for L=$L, p=$p, q=$q...")

# Create transfer matrix
T = create_pca_conditional_transfer_matrix(L, p, q, antiperiodic=antiperiodic)

println("Transfer matrix size: $(size(T))")
println("Number of non-zero elements: $(nnz(T))")

# Verify the matrix is properly normalized (rows should sum to 1)
row_sums = [sum(T[:, i]) for i in 1:size(T, 2)]
max_error = maximum(abs.(row_sums .- 1.0))
println("Maximum row sum error: $max_error")

# Analyze the matrix
results = analyze_pca_conditional_transfer_matrix(T)

# If we have steady states, analyze them
if haskey(results, "eigenvectors") && !isempty(results["steady_indices"])
    steady_idx = results["steady_indices"][1]
    steady_state = results["eigenvectors"][:, steady_idx]
    
    # Display most probable configurations
    display_most_probable_configs(steady_state, L)
    
    # Compute correlations
    # correlation_matrix, mean_density, single_site_avg, two_point_avg = compute_conditional_pca_correlations(steady_state, L)
    
    # println(@sprintf("\nSteady state mean density: %.6f", mean_density))
    # println("Single-site averages <s_i>:")
    # for i in 1:L
    #     println(@sprintf("  <s_%d> = %.6f", i, single_site_avg[i]))
    # end
end

println("\nAnalysis complete!")
