# -----------------------------------------------------------
#  helpers
# -----------------------------------------------------------

@inline bit(cfg::Int, i::Int) = (cfg >> (i-1)) & 1

@inline function swap_bits(cfg::Int, i::Int, j::Int)
    if xor(bit(cfg,i), bit(cfg,j)) == 1       # ← fixed precedence
        mask = (1 << (i-1)) | (1 << (j-1))
        cfg  = xor(cfg, mask)                 # instead of cfg ⊻= mask
    end
    return cfg
end

@inline flip_bit(cfg::Int, i::Int) = xor(cfg, 1 << (i-1))

function first_opposite(cfg::Int, i::Int, L::Int)
    sᵢ = bit(cfg,i)
    for j = i+1:L
        sᵢ != bit(cfg,j) && return j
    end
    return 0
end

# -----------------------------------------------------------
#  transition matrix
# -----------------------------------------------------------

using SparseArrays

function transition_matrix(L::Int, λ::Real; sparse::Bool=true)
    @assert 0 ≤ λ ≤ 1
    n = 1 << L                    # 2^L
    invL = 1.0/L

    rows = Int[]; cols = Int[]; vals = Float64[]

    for cfg in 0:n-1
        stay = 1.0
        for i in 1:L
            j = first_opposite(cfg,i,L)
            actprob = bit(cfg,i)==1 ? λ : 1.0
            if actprob>0
                new = (j==0) ? flip_bit(cfg,i) : swap_bits(cfg,i,j)
                push!(rows,cfg+1); push!(cols,new+1); push!(vals,invL*actprob)
                stay -= invL*actprob
            end
        end
        push!(rows,cfg+1); push!(cols,cfg+1); push!(vals,stay)
    end

    if sparse
        return sparse(rows,cols,vals,n,n)
    else
        P = zeros(n,n)
        for (r,c,v) in zip(rows,cols,vals)
            P[r,c] += v
        end
        return P
    end
end

# tiny check
L, λ = 4, 0.3
P = transition_matrix(L,λ)
@assert all(abs.(sum(P,dims=2) .- 1) .< 1e-12)
println("transition matrix (2^$L × 2^$L) built OK")
