using Plots
using Statistics  # Add this for the mean function
"""
    simulate_toom_nec(Lx, Ly, T; p=0.1, q=0.1)

Simulate Toom's NEC (North-East-Center) cellular automaton with noise.
- Lx, Ly: grid dimensions
- T: number of time steps
- p: probability of error when majority is 1
- q: probability of error when majority is 0

Returns the full state history for visualization.
"""
function simulate_toom_nec(Lx, Ly, T; p=0.1, q=0.1, x_antiperiodic=false, y_antiperiodic=false)
    # Initialize state randomly (0 or 1)
    # state = rand(0:1, Lx, Ly)    
    state = zeros(Int, Lx, Ly)
    # state[Lx,div(Ly, 2)] = 1 
    # state[20,5] = 1 


    # Set middle region to 1s (size Lx/2 × Ly/2)
    # x_start = div(Lx, 4) + 1
    # y_start = div(Ly, 4) + 1
    # x_end = x_start + div(Lx, 2) - 1
    # y_end = y_start + div(Ly, 2) - 1
    
    # for x in x_start:x_end, y in y_start:y_end
    #     state[x, y] = 1
    # end

    # Store history for visualization
    history = zeros(Int, T+1, Lx, Ly)
    densities = zeros(Float64, T+1)
    history[1, :, :] = transpose(state)
    densities[1] = mean(state)
    for t in 1:T
        # @show T
        new_state = similar(state)
        
        for x in 1:Lx, y in 1:Ly
            # Get north neighbor with selected boundary condition
            if y == Ly && y_antiperiodic
                north = 1 - state[x, 1]  # Antiperiodic in y
            else
                north = state[x, mod1(y+1, Ly)]  # Periodic in y
            end
            
            # Get east neighbor with selected boundary condition
            if x == Lx && x_antiperiodic
                east = 1 - state[1, y]  # Antiperiodic in x
            else
                east = state[mod1(x+1, Lx), y]  # Periodic in x
            end
            
            center = state[x, y]
            
            # Apply majority rule
            maj = majority(north, east, center)
            
            # Apply noise
            if maj == 1
                new_state[x, y] = rand() < p ? 0 : 1
            else
                new_state[x, y] = rand() < q ? 1 : 0
            end
        end
        
        state = new_state
        @show t, mean(state)

        history[t+1, :, :] = transpose(state)
        densities[t+1] = mean(state)
    end
    
    return history, densities
end
# Reuse the majority function from toom.jl
majority(n, e, c) = (n & e) | (n & c) | (e & c)

"""
    visualize_toom(history)
    
Create a visualization of the Toom NEC simulation.
"""
function visualize_toom(history, p, q, x_antiperiodic, y_antiperiodic)
    T, Lx, Ly = size(history)
    
    # Create folder based on boundary conditions
    folder_name = "DATA_"
    folder_name *= x_antiperiodic ? "xanti_" : "xper_"
    folder_name *= y_antiperiodic ? "yanti" : "yper"
    
    # Create directory if it doesn't exist
    mkpath(folder_name)
    
    # Create filename with boundary condition info
    filename = "$(folder_name)/image_L$(Lx)_p$(p)q$(q).gif"
    
    anim = @animate for t in 1:T
        heatmap(history[t, :, :], 
                c=:grays, 
                aspect_ratio=:equal,
                title="Toom's NEC Model (t=$t)",
                xlabel="x", ylabel="y",
                legend=false)
    end
    return gif(anim, filename, fps=5)
end

function analyze_density(densities, Lx, p, q, x_antiperiodic, y_antiperiodic)
    T = length(densities)
    
    # Create folder based on boundary conditions
    folder_name = "DATA_"
    folder_name *= x_antiperiodic ? "xanti_" : "xper_"
    folder_name *= y_antiperiodic ? "yanti" : "yper"
    
    # Create directory if it doesn't exist
    mkpath(folder_name)
    
    # Create filename with boundary condition info
    filename = "$(folder_name)/den_L$(Lx)_p$(p)q$(q).png"
    
    plot(1:T, densities, 
         xlabel="Time", ylabel="Density of 1s",
         title="Density Evolution in Toom's NEC Model",
         legend=false)
    savefig(filename)
    return densities
end

# Update the simulation run code
Lx, Ly = 20, 20
T = 10*Lx
p = 0.02
q = 0.04
@show Lx, Ly, T, p, q
xt = false
yt = false

@time history, densities = simulate_toom_nec(Lx, Ly, T, p=p, q=q, 
                                           x_antiperiodic=xt, y_antiperiodic=yt)

@time visualize_toom(history, p, q, xt, yt)
@time analyze_density(densities, Lx, p, q, xt, yt)