using LinearAlgebra
# Pauli matrices
σ₀ = [1 0; 0 1]
σ₁ = [0 1; 1 0]
σ₂ = [0 -im; im 0]
σ₃ = [1 0; 0 -1]
paulis = [σ₀, σ₁, σ₂, σ₃]

# Build the 8×8 matrix M
M = zeros(ComplexF64, 8, 8)
M[1, 1] = 1;  M[1, 2] = 1;  M[1, 3] = 1;  M[1, 5] = 1
M[8, 4] = 1;  M[8, 6] = 1;  M[8, 7] = 1;  M[8, 8] = 1

# Compute coefficients  c_{μνρ} = Tr[σ_{μνρ} * M] / 8
coeffs = Dict{NTuple{3,Int},ComplexF64}()
for μ in 0:3, ν in 0:3, ρ in 0:3
    σ = kron(kron(paulis[μ+1], paulis[ν+1]), paulis[ρ+1])
    c = tr(σ * M) / 8
    if abs(c) > 1e-12   # keep only non-zero terms
        coeffs[(μ,ν,ρ)] = c
    end
end

# Pretty-print the result
for ((μ,ν,ρ), c) in sort(collect(coeffs); by = x -> x[1])
    println("c[", μ, ν, ρ, "] = ", c)
end
