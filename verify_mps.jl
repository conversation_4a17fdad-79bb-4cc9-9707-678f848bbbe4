########################   MPS versus analytic state   ########################
#  verify_mps.jl
#
#  Usage (e.g. in the REPL):
#      include("verify_mps.jl")
#      verify_all(6)        # checks L = 2 … 6
###############################################################################

using LinearAlgebra

# ---------- χ = 3 bulk tensors ----------
const A0 = [1 0 0;
            0 0 1;
            0 0 1]

const A1 = [0 1 0;
            0 1 0;
            0 0 0]

const B  = [0 0 0;
            1 0 0;
            0 1 0]

# -----------------------------------------------------------------------------
#   Build the table  bit-string  →  (unnormalised) amplitude  from the χ = 3 MPS
# -----------------------------------------------------------------------------
function mps_amplitudes(L::Int)
    amps = Dict{BitVector,Int}()

    for idx in 0:(2^L-1)
        bits = BitVector(reverse(digits(idx, base = 2, pad = L)))  # MSB at site 1
        M = Matrix{Int}(I, 3, 3)
        for b in bits
            M *= (b == 0 ? A0 : A1)
        end
        val = tr(M * B)           # <--- coefficient  (0 or 1)
        if val != 0
            amps[bits] = Int(val) # should always be 1
        end
    end
    return amps
end

# -----------------------------------------------------------------------------
#   Build the same table from  |Ψ> = Σ_{n=1}^{2L}(X_L T^{-1})^n |0…0>
# -----------------------------------------------------------------------------
function analytic_amplitudes(L::Int)
    state = BitVector(fill(0, L))        # |0…0>
    amps  = Dict{BitVector,Int}()

    for _ in 1:(2L)
        # one application of  T^{-1}: left rotate; then X_L: flip last spin
        state = vcat(state[2:end], state[1])
        state[end] ⊻= true               # xor 1 -> flip 0↔1
        amps[copy(state)] = 1            # every generated basis state appears once
    end
    return amps
end

# -----------------------------------------------------------------------------
#   Compare the two dictionaries.  Return true if they match exactly.
# -----------------------------------------------------------------------------
function verify(L::Int; verbose::Bool = true)
    mps  = mps_amplitudes(L)
    ref  = analytic_amplitudes(L)
    ok   = mps == ref

    if verbose
        println("L = $L   →   ", ok ? "✓  match" : "✗  mismatch")
        if !ok
            extra   = setdiff(keys(mps), keys(ref))
            missing = setdiff(keys(ref), keys(mps))
            println("  extra in MPS:   ", extra)
            println("  missing in MPS: ", missing)
        end
    end
    return ok
end

# convenience wrapper
verify_all(Lmax::Int) = all(verify(L, verbose = true) for L in 2:Lmax)

###############################################################################
#   Example run
###############################################################################
if abspath(PROGRAM_FILE) == @__FILE__
    println("\nVerifying χ = 3 MPS representation …\n")
    success = verify_all(6)      # test L = 2 … 6
    println("\nOverall result: ", success ? "all tests passed 🎉" : "some test failed")
end
