using LinearAlgebra
using SparseArrays
using Printf, Statistics

⊗(A,B) = kron(A,B)
xp = [0.5, 0.5];

"""
    create_toom_1d_transfer_matrix(L, p, q; antiperiodic=false)

Create the transfer matrix for the 1D Toom's LRC model with given parameters.
The transfer matrix has dimensions 2^L × 2^L.

Parameters:
- L: Length of the 1D lattice
- p: Probability of error when majority is 1
- q: Probability of error when majority is 0
- antiperiodic: Boundary condition (true for antiperiodic, false for periodic)

Returns a sparse matrix representing the transfer operator.
"""
function create_toom_1d_transfer_matrix(L, p, q; antiperiodic=false)
    # Total number of states
    n_states = 2^L
    
    # Use sparse matrix for efficiency
    T = spzeros(Float64, n_states, n_states)
    
    # Helper function to convert state array to index
    function state_to_index(state)
        idx = 1
        for i in 1:L
            idx += state[i] * 2^(i-1)
        end
        return idx
    end
    
    # Helper function to convert index to state array
    function index_to_state(idx)
        idx = idx - 1  # Adjust for 1-based indexing
        state = zeros(Int, L)
        for i in 1:L
            state[i] = (idx >> (i-1)) & 1
        end
        return state
    end
    
    # Majority function (same as 2D version)
    majority(l, r, c) = (l & r) | (l & c) | (r & c)
    
    # Build the transfer matrix
    println("Building 1D transfer matrix (this may take a while)...")
    
    # For 1D systems, we can handle larger sizes than 2D
    if L <= 20  # Practical for systems up to length 20
        for idx_from in 1:n_states
            state_from = index_to_state(idx_from)
            
            # Calculate all possible next states and their probabilities
            for config in 0:(2^L-1)
                state_to = zeros(Int, L)
                prob = 1.0
                
                # Convert configuration to state
                for i in 1:L
                    state_to[i] = (config >> (i-1)) & 1
                end
                
                # Check if this is a valid transition and calculate probability
                for i in 1:L
                    # Get left neighbor with selected boundary condition
                    if i == 1 && antiperiodic
                        left = 1 - state_from[L]  # Antiperiodic boundary
                    else
                        left = state_from[mod1(i-1, L)]  # Periodic boundary
                    end
                    
                    # Get right neighbor with selected boundary condition  
                    if i == L && antiperiodic
                        right = 1 - state_from[1]  # Antiperiodic boundary
                    else
                        right = state_from[mod1(i+1, L)]  # Periodic boundary
                    end
                    
                    center = state_from[i]
                    
                    # Apply majority rule
                    maj = majority(left, right, center)
                    
                    # Calculate transition probability
                    if maj == 1
                        if state_to[i] == 1
                            prob *= (1 - p)  # Probability of staying 1
                        else
                            prob *= p  # Probability of flipping to 0
                        end
                    else
                        if state_to[i] == 0
                            prob *= (1 - q)  # Probability of staying 0
                        else
                            prob *= q  # Probability of flipping to 1
                        end
                    end
                end
                
                # Add to transfer matrix
                idx_to = state_to_index(state_to)
                T[idx_to, idx_from] += prob
            end
        end
    else
        # For larger systems, use Monte Carlo sampling
        println("System too large for exact matrix. Using Monte Carlo sampling...")
        
        # Number of samples per state
        n_samples = 1000
        
        for idx_from in 1:min(n_states, 100000)  # Limit sampling
            state_from = index_to_state(idx_from)
            
            # Sample next states
            for _ in 1:n_samples
                new_state = similar(state_from)
                
                for i in 1:L
                    # Get left neighbor with selected boundary condition
                    if i == 1 && antiperiodic
                        left = 1 - state_from[L]  # Antiperiodic boundary
                    else
                        left = state_from[mod1(i-1, L)]  # Periodic boundary
                    end
                    
                    # Get right neighbor with selected boundary condition  
                    if i == L && antiperiodic
                        right = 1 - state_from[1]  # Antiperiodic boundary
                    else
                        right = state_from[mod1(i+1, L)]  # Periodic boundary
                    end
                    
                    center = state_from[i]
                    
                    # Apply majority rule
                    maj = majority(left, right, center)
                    
                    # Apply noise
                    if maj == 1
                        new_state[i] = rand() < p ? 0 : 1
                    else
                        new_state[i] = rand() < q ? 1 : 0
                    end
                end
                
                # Add to transfer matrix
                idx_to = state_to_index(new_state)
                T[idx_to, idx_from] += 1.0 / n_samples
            end
        end
    end
    
    return T
end

"""
    analyze_1d_transfer_matrix(T)

Analyze the 1D transfer matrix to find steady states and relaxation times.
"""
function analyze_1d_transfer_matrix(T)
    println("Analyzing 1D transfer matrix...")
    
    # Calculate eigenvalues and eigenvectors
    if size(T, 1) <= 2^16  # Practical limit for eigendecomposition
        # Right eigendecomposition
        F_right = eigen(Array(T))
        eigenvalues_right = F_right.values
        eigenvectors_right = F_right.vectors
        
        # Left eigendecomposition (transpose the matrix)
        F_left = eigen(Array(T'))
        eigenvalues_left = F_left.values
        eigenvectors_left = F_left.vectors
        
        # Sort by magnitude of eigenvalues
        idx_right = sortperm(abs.(eigenvalues_right), rev=true)
        eigenvalues_right = eigenvalues_right[idx_right]
        eigenvectors_right = eigenvectors_right[:, idx_right]
        
        idx_left = sortperm(abs.(eigenvalues_left), rev=true)
        eigenvalues_left = eigenvalues_left[idx_left]
        eigenvectors_left = eigenvectors_left[:, idx_left]
        
        println("\n=== Right Eigenvalues (T·v = λv) ===")
        println("Largest eigenvalues:")
        for i in 1:min(10, length(eigenvalues_right))
            println("λ$i = $(eigenvalues_right[i])")
        end
        println("Right eigenvectors (first 2 columns):")
        display(eigenvectors_right[:,1:min(5, size(eigenvectors_right, 2))])
        
        # println("\n=== Left Eigenvalues (u·T = λu) ===")
        # println("Largest eigenvalues:")
        # for i in 1:min(10, length(eigenvalues_left))
        #     println("λ$i = $(eigenvalues_left[i])")
        # end
        println("Left eigenvectors (first 2 columns):")
        display(eigenvectors_left[:,1:min(2, size(eigenvectors_left, 2))])
        
        # The steady state corresponds to the right eigenvector with eigenvalue 1
        steady_idx_right = findfirst(isapprox.(eigenvalues_right, 1.0, atol=1e-10))
        if !isnothing(steady_idx_right)
            steady_state_right = eigenvectors_right[:, steady_idx_right]
            steady_state_right = steady_state_right ./ sum(steady_state_right)  # Normalize
            
            println("\nRight steady state distribution found.")
            
            # Relaxation time is related to the second largest eigenvalue
            if length(eigenvalues_right) >= 2
                relaxation_time = -1.0 / log(abs(eigenvalues_right[2]))
                println("Relaxation time: $relaxation_time")
            end
        else
            println("\nNo right steady state found with eigenvalue 1.")
        end
        
        # The left steady state
        steady_idx_left = findfirst(isapprox.(eigenvalues_left, 1.0, atol=1e-10))
        if !isnothing(steady_idx_left)
            steady_state_left = eigenvectors_left[:, steady_idx_left]
            steady_state_left = steady_state_left ./ sum(steady_state_left)  # Normalize
            
            println("\nLeft steady state distribution found.")
            
            # Check biorthogonality
            if !isnothing(steady_idx_right)
                dot_product = abs(dot(steady_state_left, steady_state_right))
                println("Dot product of left and right steady states: $dot_product")
            end
        else
            println("\nNo left steady state found with eigenvalue 1.")
        end
        
        # Return both sets of eigenvalues and eigenvectors
        return Dict(
            "right_eigenvalues" => eigenvalues_right,
            "right_eigenvectors" => eigenvectors_right,
            "left_eigenvalues" => eigenvalues_left,
            "left_eigenvectors" => eigenvectors_left
        )
    else
        println("Matrix too large for eigendecomposition.")
        return T
    end
end

"""
    visualize_1d_steady_state(steady_state, L)

Visualize the steady state distribution for different 1D configurations.
"""
function visualize_1d_steady_state(steady_state, L)
    n_states = length(steady_state)
    
    # Helper function to convert index to state array
    function index_to_state(idx)
        idx = idx - 1
        state = zeros(Int, L)
        for i in 1:L
            state[i] = (idx >> (i-1)) & 1
        end
        return state
    end
    
    println("\nSteady state probabilities for different configurations:")
    println("Configuration -> Probability")
    
    # Sort states by probability
    sorted_indices = sortperm(steady_state, rev=true)
    
    # Show top configurations
    for i in 1:min(10, n_states)
        idx = sorted_indices[i]
        config = index_to_state(idx)
        prob = steady_state[idx]
        config_str = join(config, "")
        println("$config_str -> $(round(prob, digits=6))")
    end
end

# Example usage
L = 5  # Small 1D lattice for demonstration
p = q = 0.00
# p, q = 0.001, 0.001
antiperiodic = false

println("Creating 1D transfer matrix for length $L lattice...")
T = create_toom_1d_transfer_matrix(L, p, q, antiperiodic=antiperiodic)

println("Transfer matrix size: $(size(T))")
println("Number of non-zero elements: $(nnz(T))")

# Analyze eigenvalues
# F = eigen(Array(T), sortby = λ -> -abs(λ))
# @show F.values[1:min(10, length(F.values))]
# println("Right eigenvectors (first 2 columns):")
# display(F.vectors[:,1:min(2, size(F.vectors, 2))])

# F = eigen(Array(T'), sortby = λ -> -abs(λ))
# @show F.values[1:min(10, length(F.values))]
# println("Left eigenvectors (first 2 columns):")
# display(F.vectors[:,1:min(2, size(F.vectors, 2))])

# Full analysis
results = analyze_1d_transfer_matrix(T)

"""
    compute_two_point_correlation(steady_state, L)

Compute the two-point correlation function <s_i s_j> for the 1D steady state.
Returns a matrix C where C[i,j] = <s_i s_j> - <s_i><s_j>.

Parameters:
- steady_state: The dominant right eigenvector (steady state distribution)
- L: Length of the 1D lattice

Returns:
- correlation_matrix: L×L matrix of correlations
- mean_density: Average density <s_i>
"""
function compute_two_point_correlation(steady_state, L)
    n_states = length(steady_state)

    # Helper function to convert index to state array
    function index_to_state(idx)
        idx = idx - 1
        state = zeros(Int, L)
        for i in 1:L
            state[i] = (idx >> (i-1)) & 1
        end
        return state
    end

    # Normalize steady state to ensure it's a probability distribution
    steady_state = abs.(steady_state) ./ sum(abs.(steady_state))

    # Compute single-site expectation values <s_i>
    single_site_avg = zeros(Float64, L)
    for i in 1:L
        for state_idx in 1:n_states
            state = index_to_state(state_idx)
            single_site_avg[i] += steady_state[state_idx] * (-1)^state[i]
        end
    end

    # Compute two-point expectation values <s_i s_j>
    two_point_avg = zeros(Float64, L, L)
    for i in 1:L, j in 1:L
        for state_idx in 1:n_states
            state = index_to_state(state_idx)
            two_point_avg[i, j] += steady_state[state_idx] * (-1)^(state[i] + state[j])
        end
    end

    # Compute correlation function: <s_i s_j> - <s_i><s_j>
    correlation_matrix = zeros(Float64, L, L)
    for i in 1:L, j in 1:L
        correlation_matrix[i, j] = two_point_avg[i, j] - single_site_avg[i] * single_site_avg[j]
    end

    mean_density = mean(single_site_avg)

    return correlation_matrix, mean_density, single_site_avg, two_point_avg
end

"""
    analyze_correlations(correlation_matrix, L; antiperiodic=false)

Analyze the correlation structure and compute correlation length.
"""
function analyze_correlations(correlation_matrix, L; antiperiodic=false)
    println("\n=== Correlation Analysis ===")

    # Print correlation matrix
    println("Two-point correlation matrix <s_i s_j> - <s_i><s_j>:")
    for i in 1:L
        row_str = ""
        for j in 1:L
            row_str *= @sprintf("%8.4f ", correlation_matrix[i, j])
        end
        println("Site $i: $row_str")
    end

    # Compute correlation as a function of distance
    max_distance = antiperiodic ? div(L, 2) : L-1
    distance_correlation = zeros(Float64, max_distance + 1)
    distance_counts = zeros(Int, max_distance + 1)

    for i in 1:L, j in 1:L
        if antiperiodic
            # For antiperiodic BC, distance is minimum of |i-j| and L-|i-j|
            dist = min(abs(i - j), L - abs(i - j))
        else
            # For periodic BC, use circular distance
            dist = min(abs(i - j), L - abs(i - j))
        end

        if dist <= max_distance
            distance_correlation[dist + 1] += correlation_matrix[i, j]
            distance_counts[dist + 1] += 1
        end
    end

    # Average over equivalent distances
    for d in 1:(max_distance + 1)
        if distance_counts[d] > 0
            distance_correlation[d] /= distance_counts[d]
        end
    end

    println("\nCorrelation vs distance:")
    println("Distance | Correlation")
    println("---------|------------")
    for d in 0:max_distance
        println(@sprintf("%8d | %10.6f", d, distance_correlation[d + 1]))
    end

    # Estimate correlation length (assuming exponential decay)
    if length(distance_correlation) >= 3
        # Find where correlation drops to 1/e of its maximum
        max_corr = maximum(abs.(distance_correlation[2:end]))  # Skip d=0
        target = max_corr / exp(1)

        correlation_length = NaN
        for (idx, d) in enumerate(2:lastindex(distance_correlation))
            if abs(distance_correlation[d]) <= target
                correlation_length = idx - 1  # Convert to distance (0-indexed)
                break
            end
        end

        if !isnan(correlation_length)
            println("\nEstimated correlation length: $correlation_length")
        else
            println("\nCorrelation length: > $(max_distance)")
        end
    end

    return distance_correlation
end

# Visualize steady state if available
if haskey(results, "right_eigenvectors")
    steady_state = results["right_eigenvectors"][:, 1]
    steady_state = abs.(steady_state) ./ sum(abs.(steady_state))
    visualize_1d_steady_state(steady_state, L)

    # Compute and analyze correlations
    # correlation_matrix, mean_density, single_site_avg, two_point_avg = compute_two_point_correlation(steady_state, L)

    # println("\n=== Steady State Properties ===")
    # println("Mean density: $(round(mean_density, digits=6))")
    # println("Single-site averages <s_i>:")
    # for i in 1:L
    #     println("  <s_$i> = $(round(single_site_avg[i], digits=6))")
    # end

    # distance_corr = analyze_correlations(correlation_matrix, L, antiperiodic=antiperiodic)
end
