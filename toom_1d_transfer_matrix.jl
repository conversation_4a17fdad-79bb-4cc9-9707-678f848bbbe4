using LinearAlgebra
using SparseArrays

⊗(A,B) = kron(A,B)
xp = [0.5, 0.5];

"""
    create_toom_1d_transfer_matrix(L, p, q; antiperiodic=false)

Create the transfer matrix for the 1D Toom's LRC model with given parameters.
The transfer matrix has dimensions 2^L × 2^L.

Parameters:
- L: Length of the 1D lattice
- p: Probability of error when majority is 1
- q: Probability of error when majority is 0
- antiperiodic: Boundary condition (true for antiperiodic, false for periodic)

Returns a sparse matrix representing the transfer operator.
"""
function create_toom_1d_transfer_matrix(L, p, q; antiperiodic=false)
    # Total number of states
    n_states = 2^L
    
    # Use sparse matrix for efficiency
    T = spzeros(Float64, n_states, n_states)
    
    # Helper function to convert state array to index
    function state_to_index(state)
        idx = 1
        for i in 1:L
            idx += state[i] * 2^(i-1)
        end
        return idx
    end
    
    # Helper function to convert index to state array
    function index_to_state(idx)
        idx = idx - 1  # Adjust for 1-based indexing
        state = zeros(Int, L)
        for i in 1:L
            state[i] = (idx >> (i-1)) & 1
        end
        return state
    end
    
    # Majority function (same as 2D version)
    majority(l, r, c) = (l & r) | (l & c) | (r & c)
    
    # Build the transfer matrix
    println("Building 1D transfer matrix (this may take a while)...")
    
    # For 1D systems, we can handle larger sizes than 2D
    if L <= 20  # Practical for systems up to length 20
        for idx_from in 1:n_states
            state_from = index_to_state(idx_from)
            
            # Calculate all possible next states and their probabilities
            for config in 0:(2^L-1)
                state_to = zeros(Int, L)
                prob = 1.0
                
                # Convert configuration to state
                for i in 1:L
                    state_to[i] = (config >> (i-1)) & 1
                end
                
                # Check if this is a valid transition and calculate probability
                for i in 1:L
                    # Get left neighbor with selected boundary condition
                    if i == 1 && antiperiodic
                        left = 1 - state_from[L]  # Antiperiodic boundary
                    else
                        left = state_from[mod1(i-1, L)]  # Periodic boundary
                    end
                    
                    # Get right neighbor with selected boundary condition  
                    if i == L && antiperiodic
                        right = 1 - state_from[1]  # Antiperiodic boundary
                    else
                        right = state_from[mod1(i+1, L)]  # Periodic boundary
                    end
                    
                    center = state_from[i]
                    
                    # Apply majority rule
                    maj = majority(left, right, center)
                    
                    # Calculate transition probability
                    if maj == 1
                        if state_to[i] == 1
                            prob *= (1 - p)  # Probability of staying 1
                        else
                            prob *= p  # Probability of flipping to 0
                        end
                    else
                        if state_to[i] == 0
                            prob *= (1 - q)  # Probability of staying 0
                        else
                            prob *= q  # Probability of flipping to 1
                        end
                    end
                end
                
                # Add to transfer matrix
                idx_to = state_to_index(state_to)
                T[idx_to, idx_from] += prob
            end
        end
    else
        # For larger systems, use Monte Carlo sampling
        println("System too large for exact matrix. Using Monte Carlo sampling...")
        
        # Number of samples per state
        n_samples = 1000
        
        for idx_from in 1:min(n_states, 100000)  # Limit sampling
            state_from = index_to_state(idx_from)
            
            # Sample next states
            for _ in 1:n_samples
                new_state = similar(state_from)
                
                for i in 1:L
                    # Get left neighbor with selected boundary condition
                    if i == 1 && antiperiodic
                        left = 1 - state_from[L]  # Antiperiodic boundary
                    else
                        left = state_from[mod1(i-1, L)]  # Periodic boundary
                    end
                    
                    # Get right neighbor with selected boundary condition  
                    if i == L && antiperiodic
                        right = 1 - state_from[1]  # Antiperiodic boundary
                    else
                        right = state_from[mod1(i+1, L)]  # Periodic boundary
                    end
                    
                    center = state_from[i]
                    
                    # Apply majority rule
                    maj = majority(left, right, center)
                    
                    # Apply noise
                    if maj == 1
                        new_state[i] = rand() < p ? 0 : 1
                    else
                        new_state[i] = rand() < q ? 1 : 0
                    end
                end
                
                # Add to transfer matrix
                idx_to = state_to_index(new_state)
                T[idx_to, idx_from] += 1.0 / n_samples
            end
        end
    end
    
    return T
end

"""
    analyze_1d_transfer_matrix(T)

Analyze the 1D transfer matrix to find steady states and relaxation times.
"""
function analyze_1d_transfer_matrix(T)
    println("Analyzing 1D transfer matrix...")
    
    # Calculate eigenvalues and eigenvectors
    if size(T, 1) <= 1000  # Practical limit for eigendecomposition
        # Right eigendecomposition
        F_right = eigen(Array(T))
        eigenvalues_right = F_right.values
        eigenvectors_right = F_right.vectors
        
        # Left eigendecomposition (transpose the matrix)
        F_left = eigen(Array(T'))
        eigenvalues_left = F_left.values
        eigenvectors_left = F_left.vectors
        
        # Sort by magnitude of eigenvalues
        idx_right = sortperm(abs.(eigenvalues_right), rev=true)
        eigenvalues_right = eigenvalues_right[idx_right]
        eigenvectors_right = eigenvectors_right[:, idx_right]
        
        idx_left = sortperm(abs.(eigenvalues_left), rev=true)
        eigenvalues_left = eigenvalues_left[idx_left]
        eigenvectors_left = eigenvectors_left[:, idx_left]
        
        println("\n=== Right Eigenvalues (T·v = λv) ===")
        println("Largest eigenvalues:")
        for i in 1:min(5, length(eigenvalues_right))
            println("λ$i = $(eigenvalues_right[i])")
        end
        
        println("\n=== Left Eigenvalues (u·T = λu) ===")
        println("Largest eigenvalues:")
        for i in 1:min(5, length(eigenvalues_left))
            println("λ$i = $(eigenvalues_left[i])")
        end
        
        # The steady state corresponds to the right eigenvector with eigenvalue 1
        steady_idx_right = findfirst(isapprox.(eigenvalues_right, 1.0, atol=1e-10))
        if !isnothing(steady_idx_right)
            steady_state_right = eigenvectors_right[:, steady_idx_right]
            steady_state_right = steady_state_right ./ sum(steady_state_right)  # Normalize
            
            println("\nRight steady state distribution found.")
            
            # Relaxation time is related to the second largest eigenvalue
            if length(eigenvalues_right) >= 2
                relaxation_time = -1.0 / log(abs(eigenvalues_right[2]))
                println("Relaxation time: $relaxation_time")
            end
        else
            println("\nNo right steady state found with eigenvalue 1.")
        end
        
        # The left steady state
        steady_idx_left = findfirst(isapprox.(eigenvalues_left, 1.0, atol=1e-10))
        if !isnothing(steady_idx_left)
            steady_state_left = eigenvectors_left[:, steady_idx_left]
            steady_state_left = steady_state_left ./ sum(steady_state_left)  # Normalize
            
            println("\nLeft steady state distribution found.")
            
            # Check biorthogonality
            if !isnothing(steady_idx_right)
                dot_product = abs(dot(steady_state_left, steady_state_right))
                println("Dot product of left and right steady states: $dot_product")
            end
        else
            println("\nNo left steady state found with eigenvalue 1.")
        end
        
        # Return both sets of eigenvalues and eigenvectors
        return Dict(
            "right_eigenvalues" => eigenvalues_right,
            "right_eigenvectors" => eigenvectors_right,
            "left_eigenvalues" => eigenvalues_left,
            "left_eigenvectors" => eigenvectors_left
        )
    else
        println("Matrix too large for eigendecomposition.")
        return T
    end
end

"""
    visualize_1d_steady_state(steady_state, L)

Visualize the steady state distribution for different 1D configurations.
"""
function visualize_1d_steady_state(steady_state, L)
    n_states = length(steady_state)
    
    # Helper function to convert index to state array
    function index_to_state(idx)
        idx = idx - 1
        state = zeros(Int, L)
        for i in 1:L
            state[i] = (idx >> (i-1)) & 1
        end
        return state
    end
    
    println("\nSteady state probabilities for different configurations:")
    println("Configuration -> Probability")
    
    # Sort states by probability
    sorted_indices = sortperm(steady_state, rev=true)
    
    # Show top configurations
    for i in 1:min(10, n_states)
        idx = sorted_indices[i]
        config = index_to_state(idx)
        prob = steady_state[idx]
        config_str = join(config, "")
        println("$config_str -> $(round(prob, digits=6))")
    end
end

# Example usage
L = 4  # Small 1D lattice for demonstration
p, q = 0.1, 0.1
antiperiodic = false

println("Creating 1D transfer matrix for length $L lattice...")
T = create_toom_1d_transfer_matrix(L, p, q, antiperiodic=antiperiodic)

println("Transfer matrix size: $(size(T))")
println("Number of non-zero elements: $(nnz(T))")

# Analyze eigenvalues
F = eigen(Array(T'), sortby = λ -> -abs(λ))
@show F.values[1:min(5, length(F.values))]
println("Right eigenvectors (first 2 columns):")
display(F.vectors[:,1:min(2, size(F.vectors, 2))])

F = eigen(Array(T), sortby = λ -> -abs(λ))
@show F.values[1:min(5, length(F.values))]
println("Left eigenvectors (first 2 columns):")
display(F.vectors[:,1:min(2, size(F.vectors, 2))])

# Full analysis
results = analyze_1d_transfer_matrix(T)

# Visualize steady state if available
if haskey(results, "right_eigenvectors")
    steady_state = results["right_eigenvectors"][:, 1]
    steady_state = abs.(steady_state) ./ sum(abs.(steady_state))
    visualize_1d_steady_state(steady_state, L)
end
