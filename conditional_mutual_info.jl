using LinearAlgebra
using Printf

"""
    apply_kraus_channel(ρ, kraus_ops)

Apply a quantum channel defined by Kraus operators to a density matrix.
"""
function apply_kraus_channel(ρ, kraus_ops)
    result = zeros(ComplexF64, size(ρ))
    for K in kraus_ops
        result += K * ρ * K'
    end
    return result
end

"""
    tensor_product_kraus(kraus_ops, n_sites)

Create tensor product of Kraus operators for n sites.
"""
function tensor_product_kraus(kraus_ops, n_sites)
    # Generate all combinations of Kraus operators for n sites
    n_ops = length(kraus_ops)
    total_ops = n_ops^n_sites
    
    tensor_kraus = []
    
    for i in 0:(total_ops-1)
        # Convert index to base-n_ops representation
        indices = []
        temp = i
        for _ in 1:n_sites
            push!(indices, temp % n_ops + 1)
            temp ÷= n_ops
        end
        
        # Build tensor product
        K_tensor = kraus_ops[indices[1]]
        for j in 2:n_sites
            K_tensor = kron(K_tensor, kraus_ops[indices[j]])
        end
        
        push!(tensor_kraus, K_tensor)
    end
    
    return tensor_kraus
end

"""
    partial_trace(ρ, dims, traced_systems)

Compute partial trace of a density matrix.
ρ: density matrix
dims: dimensions of each subsystem
traced_systems: indices of systems to trace out
"""
function partial_trace(ρ, dims, traced_systems)
    n_systems = length(dims)
    kept_systems = setdiff(1:n_systems, traced_systems)
    
    if isempty(kept_systems)
        return tr(ρ)
    end
    
    # Dimensions
    dim_traced = prod(dims[traced_systems])
    dim_kept = prod(dims[kept_systems])
    
    # Reshape density matrix
    total_dim = size(ρ, 1)
    
    # Create result matrix
    ρ_reduced = zeros(ComplexF64, dim_kept, dim_kept)
    
    # Compute partial trace by summing over traced indices
    for i in 1:dim_kept, j in 1:dim_kept
        # Convert reduced indices to full indices
        i_full = expand_index(i, dims, kept_systems)
        j_full = expand_index(j, dims, kept_systems)
        
        # Sum over traced systems
        for k in 1:dim_traced
            k_traced = expand_traced_index(k, dims, traced_systems)
            i_total = combine_indices(i_full, k_traced, dims, kept_systems, traced_systems)
            j_total = combine_indices(j_full, k_traced, dims, kept_systems, traced_systems)
            ρ_reduced[i, j] += ρ[i_total, j_total]
        end
    end
    
    return ρ_reduced
end

"""
    expand_index(idx, dims, systems)

Convert linear index to multi-index for specified systems.
"""
function expand_index(idx, dims, systems)
    multi_idx = zeros(Int, length(systems))
    temp = idx - 1
    for i in 1:length(systems)
        multi_idx[i] = temp % dims[systems[i]]
        temp ÷= dims[systems[i]]
    end
    return multi_idx
end

"""
    expand_traced_index(idx, dims, traced_systems)

Convert linear index to multi-index for traced systems.
"""
function expand_traced_index(idx, dims, traced_systems)
    multi_idx = zeros(Int, length(traced_systems))
    temp = idx - 1
    for i in 1:length(traced_systems)
        multi_idx[i] = temp % dims[traced_systems[i]]
        temp ÷= dims[traced_systems[i]]
    end
    return multi_idx
end

"""
    combine_indices(kept_idx, traced_idx, dims, kept_systems, traced_systems)

Combine indices from kept and traced systems to get full system index.
"""
function combine_indices(kept_idx, traced_idx, dims, kept_systems, traced_systems)
    full_idx = zeros(Int, length(dims))
    
    # Fill in kept indices
    for (i, sys) in enumerate(kept_systems)
        full_idx[sys] = kept_idx[i]
    end
    
    # Fill in traced indices
    for (i, sys) in enumerate(traced_systems)
        full_idx[sys] = traced_idx[i]
    end
    
    # Convert to linear index
    linear_idx = 1
    for i in 1:length(dims)
        linear_idx += full_idx[i] * prod(dims[1:i-1])
    end
    
    return linear_idx
end

"""
    von_neumann_entropy(ρ)

Compute von Neumann entropy S(ρ) = -tr(ρ log ρ).
"""
function von_neumann_entropy(ρ)
    # Diagonalize the density matrix
    eigenvals = real(eigvals(ρ))
    
    # Remove numerical zeros and negative eigenvalues
    eigenvals = eigenvals[eigenvals .> 1e-12]
    
    if isempty(eigenvals)
        return 0.0
    end
    
    # Compute entropy
    entropy = -sum(λ * log2(λ) for λ in eigenvals)
    return entropy
end

"""
    conditional_mutual_information(ρ_ABC, dims)

Compute conditional mutual information I(A:C|B) = S(AB) + S(BC) - S(B) - S(ABC).
"""
function conditional_mutual_information(ρ_ABC, dims)
    # Compute reduced density matrices
    ρ_AB = partial_trace(ρ_ABC, dims, [3])  # Trace out C
    ρ_BC = partial_trace(ρ_ABC, dims, [1])  # Trace out A
    ρ_B = partial_trace(ρ_ABC, dims, [1, 3])  # Trace out A and C
    
    # Compute entropies
    S_AB = von_neumann_entropy(ρ_AB)
    S_BC = von_neumann_entropy(ρ_BC)
    S_B = von_neumann_entropy(ρ_B)
    S_ABC = von_neumann_entropy(ρ_ABC)
    
    # Conditional mutual information
    I_AC_given_B = S_AB + S_BC - S_B - S_ABC
    
    return I_AC_given_B, S_AB, S_BC, S_B, S_ABC
end

"""
    main_computation(p, q)

Main function to compute conditional mutual information for given p and q.
"""
function main_computation(p, q)
    println("="^60)
    println("CONDITIONAL MUTUAL INFORMATION COMPUTATION")
    println("="^60)
    println("Parameters: p = $p, q = $q")
    
    # Define Kraus operators
    K1 = sqrt(1-q) * [1 0; 0 0]  # √(1-q)|0⟩⟨0|
    K2 = sqrt(q) * [0 0; 1 0]    # √q|1⟩⟨0|
    K3 = sqrt(1-p) * [0 0; 0 1]  # √(1-p)|1⟩⟨1|
    K4 = sqrt(p) * [0 1; 0 0]    # √p|0⟩⟨1|
    
    kraus_ops = [K1, K2, K3, K4]
    
    # println("\nKraus operators:")
    # for (i, K) in enumerate(kraus_ops)
    #     println("K$i = ")
    #     display(K)
    #     println()
    # end
    
    # Verify completeness relation: ∑ K†K = I
    completeness = sum(K' * K for K in kraus_ops)
    println("Completeness check (should be identity):")
    display(completeness)
    println("Max deviation from identity: $(maximum(abs.(completeness - I)))")
    
    # Initial state ρ = (|000⟩⟨000| + |111⟩⟨111|)/2
    ρ_initial = zeros(ComplexF64, 8, 8)
    ρ_initial[1, 1] = 0.5  # |000⟩⟨000|
    ρ_initial[8, 8] = 0.5  # |111⟩⟨111|
    
    # println("\nInitial state ρ:")
    # display(ρ_initial)
    
    # Apply channel to all three sites
    tensor_kraus = tensor_product_kraus(kraus_ops, 3)
    
    println("\nApplying quantum channel...")
    ρ_final = apply_kraus_channel(ρ_initial, tensor_kraus)
    
    # println("Final state after channel:")
    # display(ρ_final)
    
    # Verify trace preservation
    println("\nTrace of initial state: $(tr(ρ_initial))")
    println("Trace of final state: $(tr(ρ_final))")
    
    # Compute conditional mutual information
    dims = [2, 2, 2]  # Each qubit has dimension 2
    I_AC_B, S_AB, S_BC, S_B, S_ABC = conditional_mutual_information(ρ_final, dims)
    
    println("\n" * "="^40)
    println("ENTROPY ANALYSIS")
    println("="^40)
    println(@sprintf("S(AB) = %.6f", S_AB))
    println(@sprintf("S(BC) = %.6f", S_BC))
    println(@sprintf("S(B) = %.6f", S_B))
    println(@sprintf("S(ABC) = %.6f", S_ABC))
    
    println("\n" * "="^40)
    println("CONDITIONAL MUTUAL INFORMATION")
    println("="^40)
    println(@sprintf("I(A:C|B) = S(AB) + S(BC) - S(B) - S(ABC)"))
    println(@sprintf("I(A:C|B) = %.6f + %.6f - %.6f - %.6f", S_AB, S_BC, S_B, S_ABC))
    println(@sprintf("I(A:C|B) = %.6f", I_AC_B))
    
    return I_AC_B, ρ_final
end

# Example computations
println("Computing conditional mutual information for different parameter values...")

# Test case 1: p = q = 0 (no channel)

println("\n" * "="^80)
println("TEST CASE 1: p = q = 0 (identity channel)")
I1, ρ1 = main_computation(0.0, 0.0)

# Test case 2: p = q = 0.5 (maximum mixing)
println("\n" * "="^80)
println("TEST CASE 2: p = q = 0.5")
I2, ρ2 = main_computation(0.5, 0.5)

# Test case 3: p = 0.3, q = 0.2 (asymmetric)
println("\n" * "="^80)
println("TEST CASE 3: p = 0.3, q = 0.2 (asymmetric)")
I3, ρ3 = main_computation(0.3, 0.2)

println("\n" * "="^80)
println("SUMMARY")
println("="^80)
println(@sprintf("I(A:C|B) for p=0.0, q=0.0: %.6f", I1))
println(@sprintf("I(A:C|B) for p=0.5, q=0.5: %.6f", I2))
println(@sprintf("I(A:C|B) for p=0.3, q=0.2: %.6f", I3))

println("\nComputation complete!")
