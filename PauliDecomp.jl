module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

using LinearAlgebra

# ────────────────────────────────────────────────────────────────
#  basic Pauli set
# ────────────────────────────────────────────────────────────────
const σ₀ = [1 0; 0 1]          # I
const σ₁ = [0 1; 1 0]          # X
const σ₂ = [0 -im; im 0]       # Y
const σ₃ = [1 0; 0 -1]         # Z
const PAULI = (σ₀, σ₁, σ₂, σ₃)

# ────────────────────────────────────────────────────────────────
#  iterator over all length-n index tuples  (0,1,2,3)^n
# ────────────────────────────────────────────────────────────────
_indices(n) = Iterators.product(ntuple(_ -> 0:3, n)...)

# ────────────────────────────────────────────────────────────────
#  build a tensor product σ_{μ₁} ⊗ σ_{μ₂} ⊗ … ⊗ σ_{μ_n}
# ────────────────────────────────────────────────────────────────
function pauli_tensor(label::NTuple)
    op = PAULI[label[1]+1]
    for k = 2:length(label)
        op = kron(op, PAULI[label[k]+1])
    end
    return op
end

# ────────────────────────────────────────────────────────────────
#  decompose an operator into the n-qubit Pauli basis
#     returns Dict{NTuple{n,Int}, ComplexF64}
# ────────────────────────────────────────────────────────────────
function decompose(M; tol = 1e-12, group_orbits = true)
    size(M,1) == size(M,2) || error("Matrix must be square")
    n = Int(round(log2(size(M,1))))
    2^n == size(M,1)        || error("Dimension is not a power of two")

    coeffs = Dict{NTuple{n,Int},ComplexF64}()

    norm = 2.0^n            # Tr(σ_a σ_b) = 2^n δ_ab
    for label in _indices(n)
        c = tr(pauli_tensor(label) * M) / norm
        if abs(c) > tol
            coeffs[label] = c
        end
    end

    if !group_orbits
        return coeffs
    end

    # ───────── group into translation orbits ─────────
    function rotate(t::NTuple, k::Int)
        n = length(t)
        NTuple{n,Int}(t[mod1.(eachindex(t) .- k, n)])
    end
    canon(t) = minimum(rotate(t,k) for k in 0:length(t)-1)

    orbits = Dict{NTuple,Vector{ComplexF64}}()
    for (lbl,c) in coeffs
        rep = canon(lbl)
        push!(get!(orbits, rep, ComplexF64[]), c)
    end

    return coeffs, orbits
end

# ────────────────────────────────────────────────────────────────
#  pretty printer (optional)
# ────────────────────────────────────────────────────────────────
function show_coeffs(coeffs; imaginary = true)
    for (lbl,c) in sort!(collect(coeffs); by=x->x[1])
        s = join(lbl) |> reverse   # little-endian → left-most qubit
        if !imaginary && abs(imag(c)) < 1e-12
            println("%+.4f · σ_%s\n", real(c), s)
        else
            println("%+.4f%+.4fi · σ_%s\n", real(c), imag(c), s)
        end
    end
end

export decompose, show_coeffs

end  # module PauliDecomp
