using Random
using Plots

# Parameters
L = 100              # number of lattice sites
T = 100              # number of time steps
ε = 0.1             # probability of spontaneous activation

# Initialize state: random 0/1
# σ = rand(0:1, L)
σ = zeros(Int, L)

# Save history for visualization
history = zeros(Int, T, L)
history[1, :] .= σ

# Update rule
for t in 2:T
    newσ = similar(σ)
    for i in 1:L
        if rand() < ε
            newσ[i] = 1
        else
            left = σ[mod1(i-1, L)]  # periodic boundary condition
            newσ[i] = min(left, σ[i])
        end
    end
    σ .= newσ
    history[t, :] .= σ
end

# Visualization
heatmap(history, c=:grays, xlabel="Site", ylabel="Time", title="<PERSON><PERSON><PERSON>'s Process")
