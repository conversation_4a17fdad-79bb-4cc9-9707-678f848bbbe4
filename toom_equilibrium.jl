using Plots
using LinearAlgebra

"""
    find_equilibrium_density(p, q; tol=1e-10, max_iter=1000)

Numerically solve for the equilibrium density r in Toom's NEC model
given error probabilities p and q, using fixed-point iteration.

The equation is:
r = (1-q)[r^3 + 3r^2(1-r)] + p[(1-r)^3 + 3(1-r)^2*r]
"""
function find_equilibrium_density(p, q; tol=1e-10, max_iter=1000)
    # Initial guess (midpoint)
    r = 0.5
    
    # Define the right-hand side of the equation
    function rhs(r)
        term1 = (1-q) * (r^3 + 3*r^2*(1-r))
        term2 = p * ((1-r)^3 + 3*(1-r)^2*r)
        return term1 + term2
    end
    
    # Fixed-point iteration
    for i in 1:max_iter
        r_new = rhs(r)
        if abs(r_new - r) < tol
            return r_new
        end
        r = r_new
    end
    
    # If we reach here, we didn't converge
    @warn "Failed to converge after $max_iter iterations"
    return r
end

"""
    generate_phase_diagram(resolution=50)

Generate a phase diagram showing r as a function of p and q.
"""
function generate_phase_diagram(resolution=50)
    # Create grids for p and q
    p_values = range(0, 0.5, length=resolution)
    q_values = range(0, 0.5, length=resolution)
    
    # Calculate r for each (p,q) pair
    r_values = zeros(resolution, resolution)
    
    for (i, p) in enumerate(p_values)
        for (j, q) in enumerate(q_values)
            r_values[i, j] = find_equilibrium_density(p, q)
        end
    end
    
    # Create heatmap
    heatmap(p_values, q_values, transpose(r_values),
            xlabel="p", ylabel="q", title="Equilibrium Density (r)",
            color=:thermal, clims=(0,1))
    savefig("toom_phase_diagram.png")
    
    # Create contour plot
    contour_plt = contour(p_values, q_values, transpose(r_values),
                         xlabel="p", ylabel="q", title="Equilibrium Density Contours",
                         levels=10, color=:thermal)
    savefig("toom_phase_contours.png")
    
    return r_values, p_values, q_values
end

# Generate 1D plots for fixed q values
function plot_r_vs_p(q_values=[0.0, 0.1, 0.2, 0.3, 0.4, 0.5])
    p_range = range(0, 0.5, length=100)
    plt = plot(xlabel="p", ylabel="r", title="Equilibrium Density vs p", legend=:bottomright)
    
    for q in q_values
        r_values = [find_equilibrium_density(p, q) for p in p_range]
        plot!(plt, p_range, r_values, label="q = $q")
    end
    
    savefig(plt, "toom_r_vs_p.png")
    return plt
end

# Run the analysis
println("Generating phase diagram...")
r_values, p_values, q_values = generate_phase_diagram(50)
println("Generating 1D plots...")
plot_r_vs_p()
println("Analysis complete!")